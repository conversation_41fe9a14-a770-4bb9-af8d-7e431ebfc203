import React, { useState, lazy } from "react";
import { <PERSON><PERSON>on, CFormGroup, CLabel, <PERSON>ard, <PERSON>ard<PERSON>ody, <PERSON>ard<PERSON>ooter, <PERSON><PERSON>, <PERSON>rogress, CRow } from '@coreui/react';
import Select from "react-select";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import ReactDatePicker from "react-datepicker";

import MainChartContracts from './MainChartContracts.js'

const WidgetsDropdown = lazy(() => import('./WidgetsNowData.js'));

const Dashboard = () => {
  const todayOld = new Date();
  todayOld.setDate(todayOld.getDate() - 5);
  const today = new Date();

  const [selectedDatacob, setSelectedDatacob] = useState(null);
  const [selectedGrupo, setSelectedGrupo] = useState(null);
  const [groupOptions, setGroupOptions] = useState([]);
  const [selectDateBegin, setSelectDateBegin] = useState(todayOld);
  const [selectDateEnd, setSelectDateEnd] = useState(today);
  const [selectFilter, setSelectFilter] = useState(null);

  const userProfile = localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")) : "";
  const uniqueDatacob = [...new Set(userProfile.datacobs.map((item) => item))];
  const [datacobOptions] = useState([...uniqueDatacob.map((x) => ({ datacobNumber: x.datacobNumber, datacobName: "Datacob " + x.datacobName }))]);

  const handleSelectDatacob = (selectedOption) => {
    setSelectedDatacob(selectedOption);
    if (selectedOption) updateGrupoOptions(JSON.parse(JSON.stringify(selectedOption))["datacobNumber"]);
  };

  const handleClose = () => {
    setSelectedDatacob(null);
    setSelectedGrupo(null);
  };

  const handleSelectGrupo = (selectedOption) => {
    setSelectedGrupo(selectedOption);
  };

  const updateGrupoOptions = (datacobNumber) => {
    const payload = { ActiveConnection: datacobNumber };
    getGroups(payload, "getDatacobGroups").then((data) => {
      if (data) {
        const groupList = data.map((group) => ({
          id: group.id_Grupo,
          name: group.descricao,
        }));

        const allOption = { id: "", name: "Todos" };
        const optionsGroup = [allOption, ...groupList];
        setGroupOptions(optionsGroup);
      }
    }).catch((err) => {
      console.log(err);
    });
  };

  const getGroups = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const search = async () => {
    const result = {
      crm: selectedDatacob?.datacobNumber ?? '',
      groupId: selectedGrupo?.id ?? '',
      inicio: selectDateBegin.toISOString().substring(0, 10),
      fim: selectDateEnd.toISOString().substring(0, 10),
    };

    setSelectFilter(result);
  }

  return (
    <>
      <WidgetsDropdown filter={selectFilter}/>
      <CCard>
        <CCardBody>
          <CRow>
            <CCol sm="2">
              <h4 id="evolution" className="card-title mb-0">Evolução diária </h4>
              <div className="small text-muted">Entrada de Contratos</div>
            </CCol>
            <CCol sm="10" className="d-none d-md-block">
              <CButton color="primary" className="float-right" onClick={() => search()}>
                <i className="cil-search" />
              </CButton>
              <CRow>
                <CCol>
                  <CFormGroup>
                    <CLabel>CRM</CLabel>
                    <Select
                      value={selectedDatacob}
                      onChange={(value) => { if (value === null) { handleClose(); } else { handleSelectDatacob(value); } }}
                      options={datacobOptions}
                      getOptionValue={(option) => option.datacobNumber}
                      getOptionLabel={(option) => option.datacobName}
                      placeholder={"Selecione..."}
                      isClearable={true}
                    />
                  </CFormGroup>
                </CCol>
                <CCol md="4">
                  <CFormGroup>
                    <CLabel>Grupo</CLabel>
                    <Select
                      placeholder="Selecione"
                      value={selectedGrupo}
                      onChange={handleSelectGrupo}
                      options={groupOptions}
                      getOptionValue={(option) => option.id}
                      getOptionLabel={(option) => option.name}
                      isDisabled={!selectedDatacob}
                      isClearable={true}
                    />
                  </CFormGroup>
                </CCol>
                <CCol md={2}>
                  <CLabel>Data Inicio</CLabel> <br />
                  <ReactDatePicker
                    selected={selectDateBegin}
                    onChange={(e) => setSelectDateBegin(e)}
                    className="form-control w-100"
                    dateFormat="dd/MM/yyyy"
                    onKeyDown={(e) => e.preventDefault()}
                  />
                </CCol>
                <CCol md={2}>
                  <CLabel>Data Fim</CLabel> <br />
                  <ReactDatePicker
                    selected={selectDateEnd}
                    onChange={(e) => setSelectDateEnd(e)}
                    className="form-control"
                    dateFormat="dd/MM/yyyy"
                    onKeyDown={(e) => e.preventDefault()}
                  />
                </CCol>
              </CRow>
            </CCol>
          </CRow>
          <MainChartContracts style={{ height: '450px', marginTop: '40px' }} filter={selectFilter}/>
        </CCardBody>        
      </CCard>
    </>
  )
}

export default Dashboard
