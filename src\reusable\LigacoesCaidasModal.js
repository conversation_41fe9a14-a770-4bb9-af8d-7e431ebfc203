import React, { useState, useEffect } from "react";
import {
  <PERSON><PERSON>on,
  <PERSON>ard,
  CCardBody,
  CCard<PERSON>ooter,
  CCardHeader,
  CCol,
  CForm,
  CFormGroup,
  CInput,
  CLabel,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CModalTitle,
  CRow,
} from "@coreui/react";
import Select from "react-select";
import {
  listarTabulacao,
  acionarTabulacao,
  sairTabulacao,
} from "src/config/telephonyFunctions";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import LoadingComponent from "./Loading";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";

const LigacoesCaidasModal = ({ isOpen, onClose, onSubmit }) => {
  const [isLoading, setIsLoading] = useState(false);

  const [selectedGrupo, setSelectedGrupo] = useState(null);
  const [groupOptions, setGroupOptions] = useState([]);
  const [ocorrenciasOptions, setOptionsOcorrencia] = useState([]);
  const [selectedOcorrencia, setSelectedOcorrencia] = useState(null);

  const [nome, setNome] = useState("");
  const [document, setDocument] = useState("");
  const [observation, setObservation] = useState("");

  const { message } = useWebsocketTelefoniaContext();

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const updateGrupoOptions = () => {
    const payload = {
      ActiveConnection: user.activeConnection,
    };
    getGroups(payload, "getDatacobGroups")
      .then((data) => {
        if (data) {
          const groupList = data.map((group) => ({
            id: group.id_Grupo,
            name: group.descricao,
          }));

          const allOption = { id: "", name: "Nenhum" };
          const optionsGroup = [allOption, ...groupList];
          setGroupOptions(optionsGroup);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const getGroups = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  async function getTiposOcorrencia() {
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    if (tiposOcorrencia) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];
      setOptionsOcorrencia(options);
    }
  }

  async function historicoAdicionar() {
    const data = {
      id_Contrato: financiadoData.id_Contrato,
      id_Ocorrencia_Sistema: selectedOcorrencia,
      // id_Ocorrencia_Sistema: selectedOcorrencia.value,
      telefones: nome,
      observacao: observation,
      complemento: document,
      ...(financiadoData?.idLinkedGroup &&
        financiadoData?.idGrupo && {
          groupId: financiadoData?.idGrupo,
          linkedGroupId: financiadoData?.idLinkedGroup,
        }),
    };
    const ocorrencia = await POST_DATA(
      "Datacob/historicoAdicionar",
      data,
      false,
      true
    );
    return ocorrencia;
  }

  const handleSelectOcorrencia = (selectedOption) => {
    setSelectedOcorrencia(selectedOption);
  };

  const handleSelectGrupo = (selectedOption) => {
    setSelectedGrupo(selectedOption);
  };

  const handleSave = async () => {
    const callData = message;

    const ramalTactium = localStorage.getItem("ramalTactium")
      ? JSON.parse(localStorage.getItem("ramalTactium"))
      : null;

    if (
      callData.status === "Wrap" ||
      callData.status === "WrapWithPause" ||
      callData.status === "WrapWithEnding" ||
      callData.status === "WrapWithManualCall" ||
      callData.status === "WrapWithPrivateCallback"
    ) {
      const valid = validate();
      if (valid) {
        setIsLoading(true);
        const payload = {
          dispositionCode: selectedOcorrencia.cod_Ocorr_Sistema,
          agentId: callData.agentId,
          callId: callData.callId,
          // callId: callData.callId ?? callData.callIdTactium,
          description: selectedOcorrencia.label,
          ramal: ramalTactium,
        };

        await acionarTabulacao(payload);
        if (callData.agentId && callData.manualMode) {
          await sairTabulacao(callData.agentId);
        }
        await new Promise((resolve) => setTimeout(resolve, 2500));
        setIsLoading(false);
        onSubmit(true);
        resetFields();
        onClose();
      }
    }
  };

  const resetFields = () => {
    setSelectedOcorrencia(null);
    setSelectedGrupo(null);
    setNome("");
    setDocument("");
    setObservation("");
  };

  function validate() {
    if (!selectedOcorrencia.cod_Ocorr_Sistema) {
      alert("Por favor, selecione uma ocorrência do status da ligação.");
      return false;
    }
    return true;
  }

  useEffect(() => {
    if (isOpen) {
      updateGrupoOptions();
      getTiposOcorrencia();

      const callData = message;
      if (
        callData.status !== "Wrap" &&
        callData.status !== "WrapWithPause" &&
        callData.status !== "WrapWithEnding" &&
        callData.status !== "WrapWithManualCall" &&
        callData.status !== "WrapWithPrivateCallback"
      ) {
        onClose();
      }

      // webSocketTelService.socket.addEventListener("message", (event) => {
      //   let message = event.data;
      //   message = message.slice(0, -1);
      //   try {
      //     const parsedMessage = JSON.parse(message);
      //     if (parsedMessage.target === "ResponseEvent") {
      //       const callData = parsedMessage.arguments[0];
      //       if (
      //         callData.status !== "Wrap" &&
      //         callData.status !== "WrapWithPause" &&
      //         callData.status !== "WrapWithEnding" &&
      //         callData.status !== "WrapWithManualCall" &&
      //         callData.status !== "WrapWithPrivateCallback"
      //       ) {
      //         onClose();
      //       }
      //     }
      //   } catch (error) {
      //     console.error("Erro parsing JSON:", error);
      //   }
      // });
    }
  }, [isOpen]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>
        <CModalTitle>Sobre o atendimento</CModalTitle>
      </CModalHeader>
      <CModalBody className="pb-0">
        <CRow>
          <CCol>
            <CCardBody className="py-1">
              <CForm>
                <CFormGroup className="py-1 mb-2">
                  <CLabel>
                    <strong> Qual foi o status da ligação?</strong>{" "}
                    <span style={{ color: "red", fontSize: "small" }}>
                      (obrigatório)
                    </span>
                  </CLabel>
                  <Select
                    placeholder="Selecione"
                    value={selectedOcorrencia}
                    onChange={handleSelectOcorrencia}
                    options={ocorrenciasOptions}
                  />
                </CFormGroup>
                <hr />
                <CLabel>
                  <strong> Dados complementares da ligação </strong>
                </CLabel>
                <CFormGroup>
                  <CLabel>Grupo</CLabel>
                  <Select
                    className="mb-2"
                    placeholder="Nenhum"
                    value={selectedGrupo}
                    onChange={handleSelectGrupo}
                    options={groupOptions}
                    getOptionValue={(option) => option.id}
                    getOptionLabel={(option) => option.name}
                  />
                  <CLabel>Nome</CLabel>
                  <CInput
                    className="mb-2"
                    type="text"
                    value={nome}
                    onChange={(e) => setNome(e.target.value)}
                  />
                  <CLabel>CPF/CNPJ</CLabel>
                  <CInput
                    className="mb-2"
                    type="text"
                    value={document}
                    onChange={(e) => setDocument(e.target.value)}
                  />
                  <CLabel>Observação</CLabel>
                  <CInput
                    className="mb-2"
                    type="text"
                    value={observation}
                    onChange={(e) => setObservation(e.target.value)}
                  />
                </CFormGroup>
              </CForm>
            </CCardBody>
          </CCol>
          {/* <CCol>
            <CCardBody className="py-1">
              
            </CCardBody>
          </CCol> */}
        </CRow>
      </CModalBody>
      <CModalFooter>
        <CRow>
          <CCol>
            <CButton
              color="primary"
              onClick={handleSave}
              className="mr-1"
              disabled={isLoading || !selectedOcorrencia}
            >
              {isLoading ? <LoadingComponent /> : "Salvar"}
            </CButton>
          </CCol>
        </CRow>
      </CModalFooter>
    </CModal>
  );
};

export default LigacoesCaidasModal;
