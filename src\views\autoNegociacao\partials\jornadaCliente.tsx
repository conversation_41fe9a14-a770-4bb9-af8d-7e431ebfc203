import React from "react";
import { CRow, CCol } from "@coreui/react";
import "../css/jornadaCliente.css";

const periodOptions = [
  { label: "Hoje", value: "today" },
  { label: "Ontem", value: "yesterday" },
  { label: "Esta semana", value: "this_week" },
  { label: "Semana passada", value: "last_week" },
  { label: "Mês atual", value: "this_month" },
  { label: "Mês anterior", value: "last_month" },
  { label: "Dois meses atrás", value: "two_months_ago" },
  { label: "Três meses atrás", value: "three_months_ago" },
  { label: "Customizado", value: "custom" },
];

interface JornadaClienteProps {
  clients: any[];
}

const JornadaCliente: React.FC<JornadaClienteProps> = ({ clients }) => {
  const [period, setPeriod] = React.useState<string>("today");

  const startOfWeek = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day;
    return new Date(d.setDate(diff));
  };

  const [customStartDate, setCustomStartDate] = React.useState<string>("");
  const [customEndDate, setCustomEndDate] = React.useState<string>("");

  const getStartAndEndDates = (): [Date, Date] => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const end = new Date();
    end.setHours(23, 59, 59, 999);

    if (period === "custom" && customStartDate && customEndDate) {
      const startDate = new Date(customStartDate);
      const endDate = new Date(customEndDate);
      endDate.setHours(23, 59, 59, 999);
      return [startDate, endDate];
    }

    switch (period) {
      case "today":
        return [today, end];
      case "yesterday": {
        const y = new Date(today);
        y.setDate(y.getDate() - 1);
        const yEnd = new Date(y);
        yEnd.setHours(23, 59, 59, 999);
        return [y, yEnd];
      }
      case "this_week":
        return [startOfWeek(today), end];
      case "last_week": {
        const lastSun = startOfWeek(today);
        lastSun.setDate(lastSun.getDate() - 7);
        const lastSat = new Date(lastSun);
        lastSat.setDate(lastSun.getDate() + 6);
        lastSat.setHours(23, 59, 59, 999);
        return [lastSun, lastSat];
      }
      case "this_month":
        return [
          new Date(today.getFullYear(), today.getMonth(), 1),
          new Date(
            today.getFullYear(),
            today.getMonth() + 1,
            0,
            23,
            59,
            59,
            999
          ),
        ];
      case "last_month":
      case "two_months_ago":
      case "three_months_ago": {
        const monthsAgo = {
          last_month: 1,
          two_months_ago: 2,
          three_months_ago: 3,
        }[period];
        const startDate = new Date(
          today.getFullYear(),
          today.getMonth() - monthsAgo,
          1
        );
        const endDate = new Date(
          today.getFullYear(),
          today.getMonth() - monthsAgo + 1,
          0,
          23,
          59,
          59,
          999
        );
        return [startDate, endDate];
      }
      default:
        return [today, end];
    }
  };

  const [startDate, endDate] = getStartAndEndDates();

  const filteredClients = clients.filter((client) => {
    const referenceDate = new Date(client.updatedAt || client.createdAt);
    return referenceDate >= startDate && referenceDate <= endDate;
  });

  const totalConsultas = filteredClients.length;
  const totalLeads = filteredClients.filter(
    (c) => c.status !== "Consulta"
  ).length;
  const percentualLeads = totalConsultas
    ? ((totalLeads / totalConsultas) * 100).toFixed(0)
    : "0";

  const validos = filteredClients.filter(
    (c) => c.status === "PossuiContratos" || c.status === "VisualizouContratos"
  );
  const invalidos = filteredClients.filter((c) => c.status === "Lead");

  const totalValidos = validos.length;
  const totalInvalidos = invalidos.length;

  const percValidos = totalLeads
    ? ((totalValidos / totalLeads) * 100).toFixed(0)
    : "0";
  const percInvalidos = totalLeads
    ? ((totalInvalidos / totalLeads) * 100).toFixed(0)
    : "0";

  const interesseTipos = [
    "NovosNegocios",
    "Colchao",
    "Aceite",
    "Promessas",
    "Adimplencia",
  ];

  const interesseResumo = interesseTipos.map((tipo) => {
    const contratos = validos.flatMap((client) =>
      client.contracts.filter(
        (c) => c.status === "Interesse" && c.interestCategory === tipo
      )
    );
    const total = contratos.length;
    const convertidos = contratos.filter((c) => c.isConverted).length;
    const valorTotal = contratos
      .filter((c) => c.isConverted)
      .reduce((acc, c) => acc + (c.negotiationAmount ?? 0), 0);

    return {
      tipo,
      total,
      convertidos,
      naoConvertidos: total - convertidos,
      percentual: total ? ((convertidos / total) * 100).toFixed(2) : "0.00",
      valorTotal,
    };
  });

  const totalRadar = validos
    .flatMap((c) => c.contracts)
    .filter((c) => c.status === "Radar").length;

  const totalAbandonado = validos
    .flatMap((c) => c.contracts)
    .filter((c) => c.status === "Abandonado").length;

  return (
    <>
      <div className="row mb-3 align-items-end">
        <div className="col-auto">
          <label className="form-label">
            Período:
            {period !== "custom" && (
              <span className="text-muted">
                {" "}
                {period === "today" || period === "yesterday"
                  ? startDate.toLocaleDateString()
                  : `${startDate.toLocaleDateString()} até ${endDate.toLocaleDateString()}`}
              </span>
            )}
          </label>
          <select
            className="form-control"
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
          >
            {periodOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {period === "custom" && (
          <>
            <div className="col-auto">
              <label className="form-label">Início</label>
              <input
                type="date"
                className="form-control"
                value={customStartDate}
                onChange={(e) => setCustomStartDate(e.target.value)}
              />
            </div>
            <div className="col-auto">
              <label className="form-label">Fim</label>
              <input
                type="date"
                className="form-control"
                value={customEndDate}
                onChange={(e) => setCustomEndDate(e.target.value)}
              />
            </div>
          </>
        )}
      </div>
      <CRow>
        <CCol lg={3} md={6} sm={12}>
          <div className="coluna">
            <div className="coluna-titulo">Todas as consultas feitas</div>
            <div className="bloco azul">
              <div className="titulo">
                Consultas - <span className="valor">{totalConsultas}</span>
              </div>
            </div>
            <div className="bloco laranja">
              <div className="titulo">
                Leads -
                <span className="valor">
                  {totalLeads}{" "}
                  <span className="porcentagem">({percentualLeads}%)</span>
                </span>
              </div>
            </div>
          </div>
        </CCol>
        <CCol lg={3} md={6} sm={12}>
          <div className="coluna">
            <div className="coluna-titulo">
              N1 - Documento foi encontrado e foi exibida ao menos uma dívida
            </div>
            <div className="bloco verde">
              <div className="titulo">
                Validados -
                <span className="valor">
                  {totalValidos}{" "}
                  <span className="porcentagem">({percValidos}%)</span>
                </span>
              </div>
            </div>
            <div className="bloco vermelho">
              <div className="titulo">
                Não Validou -
                <span className="valor">
                  {totalInvalidos}{" "}
                  <span className="porcentagem">({percInvalidos}%)</span>
                </span>
              </div>
            </div>
          </div>
        </CCol>
        <CCol lg={6} md={12} sm={12}>
          <div className="coluna">
            <CRow>
              <CCol md={5}>
                <div className="coluna-titulo">
                  N2 - Interagiu com a informação apresentada
                </div>
              </CCol>
              <CCol md={7}>
                <div className="coluna-titulo">N3 - Conversão</div>
              </CCol>
            </CRow>
            {interesseResumo.map(
              ({ tipo, total, convertidos, naoConvertidos, valorTotal }) => (
                <div key={tipo}>
                  <CRow>
                    <CCol md={5}>
                      <div className="bloco ciano">
                        <div className="titulo">
                          Interesse -{" "}
                          {tipo
                            .replace(/([A-Z])/g, " $1")
                            .replace(/^./, (l) => l.toUpperCase())
                            .trim()}
                          <span className="valor"> - {total}</span>
                        </div>
                      </div>
                    </CCol>
                    <CCol md={7}>
                      <CRow>
                        <CCol md={6}>
                          <div className="bloco verde">
                            <div className="titulo">Convertido </div>
                            <div className="valor">
                              {convertidos} (
                              {((convertidos / total) * 100 || 0).toFixed(2)}%)
                            </div>
                            <div className="valor">
                              R${" "}
                              {valorTotal.toLocaleString("pt-BR", {
                                minimumFractionDigits: 2,
                              })}
                            </div>
                          </div>
                        </CCol>
                        <CCol md={6}>
                          <div className="bloco vermelho">
                            <div className="titulo">Sem conversão</div>
                            <div className="valor">
                              {naoConvertidos} (
                              {((naoConvertidos / total) * 100 || 0).toFixed(2)}
                              %)
                            </div>
                          </div>
                        </CCol>
                      </CRow>
                    </CCol>
                  </CRow>
                </div>
              )
            )}
            <CRow>
              <CCol md={5}>
                <div className="bloco roxo">
                  <div className="titulo">
                    Radar -<span className="valor"> {totalRadar}</span>
                  </div>
                </div>
              </CCol>
            </CRow>
            <CRow>
              <CCol md={5}>
                <div className="bloco vermelho">
                  <div className="titulo">
                    Abandono -<span className="valor"> {totalAbandonado}</span>
                  </div>
                </div>
              </CCol>
            </CRow>
          </div>
        </CCol>
      </CRow>
    </>
  );
};

export default JornadaCliente;
