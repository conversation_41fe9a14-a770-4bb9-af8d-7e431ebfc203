import React from "react";
import { CChartBar } from "@coreui/react-chartjs";
import { formatDate } from "../utils/helpers.tsx";

interface ConsultasLeadsPorHoraProps {
  clients: any[];
}
const ConsultasLeadsPorHora: React.FC<ConsultasLeadsPorHoraProps> = ({
  clients,
}) => {
  const consultasPorHora = Array(24).fill(0);
  const leadsPorHora = Array(24).fill(0);
  const hojeFormatado = formatDate(new Date().toISOString());
  clients.forEach((client) => {
    const dataBase = client.updatedAt?.trim()
      ? client.updatedAt
      : client.createdAt;
    if (!dataBase) return;

    const dataFormatada = formatDate(dataBase);

    if (dataFormatada !== hojeFormatado) return;

    const hora = new Date(dataBase.replace(" ", "T")).getHours();
    consultasPorHora[hora]++;
    if (client.status !== "Consulta") {
      leadsPorHora[hora]++;
    }
  });

  const datasets = [
    {
      label: "Consultas",
      backgroundColor: "#2f65f6",
      data: consultasPorHora,
    },
    {
      label: "Leads",
      backgroundColor: "#f76c2f",
      data: leadsPorHora,
    },
  ];

  const options = {
    responsive: true,
    plugins: {
      legend: { position: "bottom" },
    },
    scales: {
      yAxes: [
        {
          scaleLabel: {
            display: true,
            labelString: "Quantidade",
          },
          ticks: {
            beginAtZero: true,
            stepSize: 1,
            precision: 0,
          },
          gridLines: {
            display: false,
          },
        },
      ],
      xAxes: [
        {
          scaleLabel: {
            display: true,
            labelString: "Horas",
          },
          gridLines: {
            display: true,
          },
        },
      ],
    },
  };

  return (
    <div>
      <CChartBar
        labels={Array.from({ length: 24 }, (_, i) => `${i}h`)}
        datasets={datasets}
        options={options}
      />
    </div>
  );
};

export default ConsultasLeadsPorHora;
