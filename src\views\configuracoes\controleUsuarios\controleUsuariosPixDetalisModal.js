import React from "react";
import {
  C<PERSON>odal<PERSON>ooter,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CButton,
  CTooltip,
} from "@coreui/react";
import { formatCurrency, formatDate } from "src/reusable/helpers";

const ControleUsuariosPixDetalisModal = ({
  isOpen,
  onClose,
  ticket = null,
}) => {
  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalHeader closeButton>
        <CModalTitle></CModalTitle>
      </CModalHeader>
      <CModalBody>
        <div
          className="table-responsive"
          style={{ overflow: "auto", maxHeight: "500px" }}
        >
          <table className="table">
            <thead>
              <tr>
                <th>Nome</th>
                <th>Nr. Contrato</th>
                <th>EMP</th>
                <th>Parcelas</th>
                <th>Valor</th>
                <th>Emiss<PERSON></th>
                <th>Status</th>
                <th><PERSON><PERSON><PERSON></th>
                <th>Empreendimento</th>
                <th>Usuário</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>{ticket?.nomeCliente}</td>
                <td>{ticket?.numeroContrato}</td>
                <td>{ticket?.emp}</td>
                <td>
                  {ticket.primeiraParcela}{" "}
                  {ticket?.parcelas?.includes(",") && (
                    <CTooltip content={ticket.parcelas}>
                      <span
                        style={{ cursor: "default" }}
                        className="badge badge-info"
                      >
                        +
                      </span>
                    </CTooltip>
                  )}
                </td>
                <td>{formatCurrency(ticket?.valor)}</td>
                <td>{formatDate(ticket?.dataEmissao)}</td>
                <td>{ticket?.statusPix}</td>
                <td>{ticket?.descricaoCarteira}</td>
                <td>{ticket?.carteiraEmpreendimento}</td>
                <td>{ticket?.nomeUsuario}</td>
              </tr>
            </tbody>
          </table>
        </div>
        <br />
      </CModalBody>
      <CModalFooter>
        <CButton color="danger" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ControleUsuariosPixDetalisModal;
