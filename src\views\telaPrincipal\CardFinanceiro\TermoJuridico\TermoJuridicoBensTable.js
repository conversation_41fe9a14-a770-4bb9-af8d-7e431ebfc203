import { CModalBody } from "@coreui/react";
import React from "react";

const TermoJuridicoBensTable = ({ bensData, onSelect }) => {
  const handleSelect = (option) => {
    onSelect(option);
  };

  return (
    <>
      <CModalBody>
        <h5>Selecione o veículo:</h5>
        <div className="table-responsive">
          <table className="table table-bordered table-hover">
            <thead>
              <tr>
                <th>Tipo</th>
                <th>Contrato</th>
                <th>Descrição</th>
                <th>Marca</th>
                <th>Modelo</th>
                <th>Ano Fab/Mod</th>
                <th>Cor</th>
                <th>Placa</th>
                <th>Renavam</th>
                <th>Dt. Venda</th>
                <th>Vl. Venda</th>
              </tr>
            </thead>
            <tbody>
              {bensData?.map((bem, index) => (
                <tr
                  key={`tr${index}`}
                  onClick={() => handleSelect(bem)}
                  className={"cursor-pointer"}
                  style={{
                    cursor: "pointer",
                  }}
                >
                  <td>{bem.tipo}</td>
                  <td>{bem.contrato}</td>
                  <td>{bem.descricao}</td>
                  <td>{bem.marca}</td>
                  <td>{bem.modelo}</td>
                  <td>{bem.ano_Fabricacao}</td>
                  <td>{bem.cor}</td>
                  <td>{bem.placa}</td>
                  <td>{bem.renavam}</td>
                  <td>{bem.dt_Venda}</td>
                  <td>{bem.vl_Venda}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CModalBody>
    </>
  );
};

export default TermoJuridicoBensTable;
