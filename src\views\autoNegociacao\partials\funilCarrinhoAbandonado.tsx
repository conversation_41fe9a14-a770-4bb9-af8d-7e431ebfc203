import React from "react";
const etapas = [
  { label: "Passo 0 - Consulta do documento", key: "Consulta", cor: "#ff4d4f" },
  {
    label: "Passo 1 - Apresentação dos contratos",
    key: "Contratos",
    cor: "#ff7a45",
  },
  {
    label: "Passo 2 - Negociação sem interesse",
    key: "SemInteresse",
    cor: "#ffa940",
  },
  { label: "Passo 3 - Negociação Pesquisa", key: "Pesquisa", cor: "#fadb14" },
  {
    label: "Passo 4 - Negociação Contraproposta",
    key: "Contraproposta",
    cor: "#bae637",
  },
  {
    label: "Passo 5 - Negociação Transbordo - Multicanalidade",
    key: "Multicanalidade",
    cor: "#52c41a",
  },
  {
    label: "Passo 6 - Negociação Formulário de atualização cadastro",
    key: "Cadastro",
    cor: "#389e0d",
  },
  {
    label: "Passo 7 - Checkout Sem interesse",
    key: "Checkout",
    cor: "#237804",
  },
];

const periodOptions = [
  { label: "Hoje", value: "today" },
  { label: "Ontem", value: "yesterday" },
  { label: "Esta semana", value: "this_week" },
  { label: "Semana passada", value: "last_week" },
  { label: "Mês atual", value: "this_month" },
  { label: "Mês anterior", value: "last_month" },
  { label: "Dois meses atrás", value: "two_months_ago" },
  { label: "Três meses atrás", value: "three_months_ago" },
  { label: "Customizado", value: "custom" },
];

interface FunilCarrinhoAbandonadoProps {
  clients: any[];
}
const FunilCarrinhoAbandonado: React.FC<FunilCarrinhoAbandonadoProps> = ({
  clients,
}) => {
  const [period, setPeriod] = React.useState<string>("today");
  const [customStart, setCustomStart] = React.useState<string>("");
  const [customEnd, setCustomEnd] = React.useState<string>("");

  const startOfWeek = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day;
    return new Date(d.setDate(diff));
  };

  const getStartAndEndDates = (): [Date, Date] => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const end = new Date();
    end.setHours(23, 59, 59, 999);

    switch (period) {
      case "today":
        return [today, end];
      case "yesterday": {
        const y = new Date(today);
        y.setDate(y.getDate() - 1);
        const yEnd = new Date(y);
        yEnd.setHours(23, 59, 59, 999);
        return [y, yEnd];
      }
      case "this_week":
        return [startOfWeek(today), end];
      case "last_week": {
        const lastSun = startOfWeek(today);
        lastSun.setDate(lastSun.getDate() - 7);
        const lastSat = new Date(lastSun);
        lastSat.setDate(lastSun.getDate() + 6);
        lastSat.setHours(23, 59, 59, 999);
        return [lastSun, lastSat];
      }
      case "this_month":
        return [
          new Date(today.getFullYear(), today.getMonth(), 1),
          new Date(
            today.getFullYear(),
            today.getMonth() + 1,
            0,
            23,
            59,
            59,
            999
          ),
        ];
      case "last_month":
      case "two_months_ago":
      case "three_months_ago": {
        const monthsAgo = {
          last_month: 1,
          two_months_ago: 2,
          three_months_ago: 3,
        }[period];
        const startDate = new Date(
          today.getFullYear(),
          today.getMonth() - monthsAgo,
          1
        );
        const endDate = new Date(
          today.getFullYear(),
          today.getMonth() - monthsAgo + 1,
          0,
          23,
          59,
          59,
          999
        );
        return [startDate, endDate];
      }
      case "custom": {
        const startDate = new Date(customStart);
        const endDate = new Date(customEnd);
        endDate.setHours(23, 59, 59, 999);
        return [startDate, endDate];
      }
      default:
        return [today, end];
    }
  };

  const [startDate, endDate] = getStartAndEndDates();

  const filteredClients = clients.filter((client) => {
    const referenceDate = new Date(client.updatedAt || client.createdAt);
    return referenceDate >= startDate && referenceDate <= endDate;
  });

  const total = filteredClients.length;

  const contagemEtapas = etapas.map(({ key, label, cor }) => {
    let count = 0;

    switch (key) {
      case "Consulta":
        count = filteredClients.filter((client) =>
          [
            "Consulta",
            "Lead",
            "PossuiContratos",
            "VisualizouContratos",
          ].includes(client.status)
        ).length;
        break;
      case "Contratos":
        count = filteredClients.filter((client) =>
          ["PossuiContratos", "VisualizouContratos"].includes(client.status)
        ).length;
        break;
      case "SemInteresse":
        count = filteredClients.filter(
          (client) =>
            ["PossuiContratos", "VisualizouContratos"].includes(
              client.status
            ) &&
            client.contracts.some(
              (c) =>
                c.status === "Abandonado" &&
                c.abandonmentReason === "SemInteresse"
            )
        ).length;
        break;
      case "Pesquisa":
        count = filteredClients.filter(
          (client) =>
            client.status === "VisualizouContratos" &&
            client.contracts.some(
              (c) =>
                c.status === "Abandonado" && c.abandonmentReason === "Pesquisa"
            )
        ).length;
        break;
      case "Contraproposta":
        count = filteredClients.filter(
          (client) =>
            client.status === "VisualizouContratos" &&
            client.contracts.some(
              (c) =>
                c.status === "Abandonado" &&
                c.abandonmentReason === "Contraproposta"
            )
        ).length;
        break;
      case "Multicanalidade":
        count = filteredClients.filter(
          (client) =>
            client.status === "VisualizouContratos" &&
            client.contracts.some(
              (c) =>
                c.status === "Abandonado" &&
                c.abandonmentReason === "Multicanalidade"
            )
        ).length;
        break;
      case "Cadastro":
        count = filteredClients.filter(
          (client) =>
            client.status === "VisualizouContratos" &&
            client.contracts.some(
              (c) =>
                c.status === "Abandonado" && c.abandonmentReason === "Cadastro"
            )
        ).length;
        break;
      case "Checkout":
        count = filteredClients.filter(
          (client) =>
            client.status === "VisualizouContratos" &&
            client.contracts.some(
              (c) =>
                c.status === "Abandonado" && c.abandonmentReason === "Checkout"
            )
        ).length;
        break;
    }

    const percentual = total ? (count / total) * 100 : 0;
    return { label, key, count, percentual, cor };
  });

  return (
    <>
      <div className="row mb-3 align-items-end">
        <div className="col-auto">
          <label className="form-label">
            Período:
            {period !== "custom" && (
              <span className="text-muted">
                {" "}
                {period === "today" || period === "yesterday"
                  ? startDate.toLocaleDateString()
                  : `${startDate.toLocaleDateString()} até ${endDate.toLocaleDateString()}`}
              </span>
            )}
          </label>
          <select
            className="form-control"
            value={period}
            onChange={(e) => setPeriod(e.target.value)}
          >
            {periodOptions.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>

        {period === "custom" && (
          <>
            <div className="col-auto">
              <label className="form-label">Início</label>
              <input
                type="date"
                className="form-control"
                value={customStart}
                onChange={(e) => setCustomStart(e.target.value)}
              />
            </div>
            <div className="col-auto">
              <label className="form-label">Fim</label>
              <input
                type="date"
                className="form-control"
                value={customEnd}
                onChange={(e) => setCustomEnd(e.target.value)}
              />
            </div>
          </>
        )}
      </div>

      <div>Total de leads: {total}</div>
      <div className="d-flex justify-content-between text-center mb-3">
        {contagemEtapas.map((etapa) => (
          <div
            key={etapa.key}
            style={{ width: `${100 / contagemEtapas.length}%` }}
          >
            <div style={{ fontWeight: "bold", color: etapa.cor }}>
              {etapa.count}
            </div>
            <div style={{ fontSize: "0.75rem", color: etapa.cor }}>
              ({etapa.percentual.toFixed(2)}%)
            </div>
            <div style={{ fontSize: "0.75rem" }}>{etapa.label}</div>
          </div>
        ))}
      </div>

      <div
        style={{
          width: "100%",
          height: "10px",
          borderRadius: "8px",
          background: `linear-gradient(to right, ${etapas
            .map((e) => e.cor)
            .join(", ")})`,
          transition: "all 0.4s ease-in-out",
        }}
      />
    </>
  );
};

export default FunilCarrinhoAbandonado;
