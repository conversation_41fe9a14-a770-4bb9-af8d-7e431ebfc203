import React from "react";
import { useHistory } from "react-router-dom";
import { useState } from "react";
import { CDropdown, CDropdownItem, CDropdownMenu, CDropdownToggle, CImg } from "@coreui/react";
import EditProfileModal from "src/reusable/EditProfileHeaderModal.js";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import { useMyContext } from "src/reusable/DataContext";
import { POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";

const TheHeaderDropdown = () => {
  const {
    updateData,
    updateCustas,
    updateCustasProjuris,
    updateAppSettings,
    appSettings,
  } = useMyContext();

  const { connection, LogoutAgente, message, getIsActiveTelephony } =
    useWebsocketTelefoniaContext();

  const [showModal, setShowModal] = useState(false);
  const [userProfile] = useState(
    JSON.parse(localStorage.getItem("userProfile"))
  );
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const history = useHistory();

  const getUpdatedCallData = () => {
    const callAtualizada = localStorage.getItem("callData")
      ? JSON.parse(localStorage.getItem("callData"))
      : null;
    return callAtualizada;
  };

  const addLogLoginUser = async (tipo, responseApi, userName) => {
    try {
      const data = { type: tipo, responseApi: responseApi, userName: userName };
      await POST_DATA(getURI("authLogUser"), data, true);
    } catch (error) {
      console.error("Erro:", error);
    }
  };

  const handleClick = async () => {
    if (user.telephonyId && user.telephonyId > 0 && getIsActiveTelephony()) {
      const callData = message;
      if (callData?.status) {
        if (
          callData.status === "Talking" ||
          callData.status === "TalkingWithManualCall" ||
          callData.status === "TalkingWithPause" ||
          callData.status === "Wrap" ||
          callData.status === "WrapWithManualCall" ||
          callData.status === "WrapWithPrivateCallback" ||
          callData.status === "WrapWithPause"
        ) {
          toast.info(
            "Logout da telefonia solicitado. Será desconectado da telefonia após terminar e qualificar a ligação atual."
          );
          await LogoutAgente();
          return;
        }
        if (
          callData.status === "TalkingWithEnding"
          // || callData.status === "WrapWithEnding"
        ) {
          toast.info(
            "Logout da telefonia solicitado. Será desconectado da telefonia após terminar e qualificar a ligação atual."
          );
          return;
        } else {
          const notifyId = toast.loading("Desconectando da telefonia...", {
            autoClose: false,
          });
          try {
            LogoutAgente();
            await new Promise((resolve) => setTimeout(resolve, 2200));
            toast.dismiss(notifyId);
          } catch {
            console.error("Falha na API de deslogar");
          } finally {
            toast.dismiss(notifyId);
          }
        }
      }
      await connection
        .stop()
        .then(() => console.log("WebSocket SignalR foi fechado."))
        .catch((error) =>
          console.error("Erro ao fechar o WebSocket SignalR:", error)
        );
    }
    await addLogLoginUser("logout", "Usuario Deslogado", user.username);
    localStorage.clear();
    updateData(null);
    updateCustas(null);
    updateCustasProjuris(null);
    history.push("/login");
    window.location.reload();
  };

  const handleEditProfile = () => {
    setShowModal(true);
  };

  const handleClickTicketChart = () => {
    updateAppSettings({ ...appSettings, showVisaoBoleto: true });
  };

  return (
    <>
      <CDropdown inNav className="c-header-nav-items mx-2" direction="down">
        <CDropdownToggle className="c-header-nav-link" caret={false}>
          <div className="c-avatar">
            <CImg
              src={
                userProfile?.profileImage
                  ? userProfile?.profileImage
                  : "avatars/default.jpg"
              }
              className="c-avatar-img"
              alt="User avatar"
            />
          </div>
        </CDropdownToggle>
        <CDropdownMenu className="pt-0" placement="bottom-end">
          {/* <CDropdownItem onClick={handleEditProfile}>
            <i className="cil-arrow-circle-top mr-1" />
            Editar Perfil
            <CBadge color="info" className="mfs-auto">42</CBadge>
          </CDropdownItem> */}
          <CDropdownItem to="/dashboard/Dashboard">
            <i className="cil-chart-pie mfe-2" />
            Dashboard
          </CDropdownItem>
          <CDropdownItem onClick={handleClickTicketChart}>
            <i className="cil-chart-pie mfe-2" />
            Visão de Boletos
          </CDropdownItem>
          <CDropdownItem onClick={handleClick}>
            <i className="cil-account-logout mfe-2" />
            Logout
          </CDropdownItem>
        </CDropdownMenu>
      </CDropdown>
      {showModal && <EditProfileModal onClose={() => setShowModal(false)} />}
    </>
  );
};

export default TheHeaderDropdown;
