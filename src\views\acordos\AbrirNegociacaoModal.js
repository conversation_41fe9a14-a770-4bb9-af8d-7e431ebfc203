import React, { useState, useEffect } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
  CNav,
  CCard,
  CTabs,
  CTabPane,
  CTabContent,
  CNavLink,
  CCol,
  CNavItem,
  CRow,
  CTooltip,
  CCardBody,
  CDataTable,
  CButton,
} from "@coreui/react";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import { GET_DATA } from "src/api";
import { useAuth } from "src/auth/AuthContext";
import DetalhesBoletoModal from "./DetalhesBoletoModal";
import DetalhesReciboModal from "./DetalhesReciboModal";
import CancelarBoletoModal from "./CancelarBoletoModal";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";

const AbrirNegociacaoModal = ({ isOpen, onClose, dados }) => {
  const { checkPermission, inforPermissions } = useAuth();
  const permissao = {
    modulo: "Visualizar Acordos",
    submodulo: null,
  };
  const permissaoNegociacao = {
    modulo: "Negociação",
    submodulo: "Simular",
  };

  const permissaoNegociacaoDetalhesCalculo = {
    modulo: "Negociação",
    submodulo: "Simular - Detalhes do Cálculo",
  };

  const permissaoNegociacaoBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  };

  const permissaoNegociacaoBoletoDetalhes = {
    modulo: "Negociação",
    submodulo: "Boleto - Detalhes do Boleto",
  };

  const [dadosAcordo, setDadosAcordo] = useState(null);

  const [idParcela, setIdParcela] = useState(null);
  const [numParcela, setNumParcela] = useState(null);
  const [boletoSelecionado, setBoletoSelecionado] = useState(null);

  const [tableBoletos, setTableBoletos] = useState([]);
  const [tableRecibos, setTableRecibos] = useState(null);
  const [tableNegociacoes, setTableNegociacoes] = useState(null);

  const [showModalDetalhesBoleto, setShowModalDetalhesBoleto] = useState(false);
  const [showModalDetalhesRecibo, setShowModalDetalhesRecibo] = useState(false);
  const [showModalCancelarBoleto, setShowModalCancelarBoleto] = useState(false);
  const [detalhesBoleto, setDetalhesBoleto] = useState(null);
  const [detalhesRecibo, setDetalhesRecibo] = useState(null);

  const financiadoData = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const parcelasColumns = [
    { key: "nr_Parcela", label: "Parcela" },
    { key: "nr_Plano", label: "Plano" },
    {
      key: "dt_Vencimento",
      label: "Dt. Vencimento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "vl_Saldo", label: "Valor Saldo", _style: { whiteSpace: "nowrap" } },
    {
      key: "vl_Atualizado",
      label: "Valor Atualizado",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "vl_Pago", label: "Valor Pago", _style: { whiteSpace: "nowrap" } },
    {
      key: "dt_Pagamento",
      label: "Dt. Pagamento.",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "atraso", label: "Atraso" },
    {
      key: "dt_Atualizacao",
      label: "Dt. Atualização",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Venc_Boleto",
      label: "Dt. Vencimento Boleto",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "linha_Digitavel",
      label: "Linha Digitável",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "nr_Boleto",
      label: "Nosso Número",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "url_Boleto",
      label: "URL Boleto",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "vl_Honor",
      label: "Valor Honorário",
      _style: { whiteSpace: "nowrap" },
    },
  ];

  const boletosFields = [
    { key: "detalhes" },
    { key: "visualizar" },
    { key: "nr_Boleto" },
    { key: "dt_Venc", label: "Vencimento" },
    {
      key: "vl_Boleto",
      label: "Valor Boleto",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "status" },
    { key: "boleto_Emitido", _style: { whiteSpace: "nowrap" } },
    {
      key: "dt_Pago",
      label: "Data Pagamento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "vl_Pago", label: "Valor Pago", _style: { whiteSpace: "nowrap" } },
    { key: "dt_Inco", _style: { whiteSpace: "nowrap" } },
    { key: "codigo" },
  ];

  const recibosFields = [
    { key: "detalhes" },
    { key: "nr_Recibo", _style: { whiteSpace: "nowrap" } },
    { key: "nr_Boleto", _style: { whiteSpace: "nowrap" } },
    { key: "recibo_Sobre", _style: { whiteSpace: "nowrap" } },
    { key: "status" },
    { key: "vl_Pago", label: "Valor Pago", _style: { whiteSpace: "nowrap" } },
    {
      key: "dt_Emiss",
      label: "Data Emissão",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Pagto",
      label: "Data Pagamento",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "descricao_Motivo_Cancel",
      label: "Motivo Cancelamento",
      _style: { whiteSpace: "nowrap" },
    },
  ];

  const negociacaoFields = [
    { key: "status" },
    { key: "liberado" },
    { key: "descricao" },
    {
      key: "dt_Negociacao",
      label: "Data Negociação",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "vl_Negociacao",
      label: "Valor Negociação",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "negociacao_Enviada", _style: { whiteSpace: "nowrap" } },
    {
      key: "dt_Cadastro_Negociacao",
      label: "Data Inclusão",
      _style: { whiteSpace: "nowrap" },
    },
  ];

  const tabs = [
    {
      id: 1,
      label: "Boletos",
      icon: "cil-barcode",
      content: (
        <div>
          {tableBoletos ? (
            <CDataTable
              items={tableBoletos}
              fields={boletosFields}
              hover
              responsive
              scopedSlots={{
                detalhes: (item) => (
                  <td>
                    <CButton
                      className="button-link py-0 px-0"
                      onClick={() => handleDetalhesBoleto(item)}
                      title={
                        inforPermissions(permissaoNegociacaoBoletoDetalhes).view
                      }
                      disabled={
                        !checkPermission(
                          permissaoNegociacaoBoletoDetalhes.modulo,
                          "View",
                          permissaoNegociacaoBoletoDetalhes.submodulo
                        )
                      }
                    >
                      Detalhes
                    </CButton>
                  </td>
                ),
                visualizar: (item) => (
                  <td>
                    <CButton
                      className="button-link py-0 px-0"
                      onClick={() => handleVisualizarBoleto(item)}
                      title={inforPermissions(permissaoNegociacaoBoleto).view}
                      disabled={
                        !checkPermission(
                          permissaoNegociacaoBoleto.modulo,
                          "View",
                          permissaoNegociacaoBoleto.submodulo
                        )
                      }
                    >
                      Visualizar
                    </CButton>
                  </td>
                ),
                nr_Boleto: (item) => (
                  <td className="nowrap-cell">{item.nr_Boleto}</td>
                ),
                dt_Venc: (item) =>
                  item.dt_Venc ? (
                    <td>{formatDate(item.dt_Venc)}</td>
                  ) : (
                    <td>---</td>
                  ),
                dt_Pago: (item) =>
                  item.dt_Pago ? (
                    <td>{formatDate(item.dt_Pago)}</td>
                  ) : (
                    <td>---</td>
                  ),
                dt_Inco: (item) =>
                  item.dt_Inco ? (
                    <td>{formatDate(item.dt_Inco)}</td>
                  ) : (
                    <td>---</td>
                  ),
                vl_Boleto: (item) =>
                  item.vl_Boleto ? (
                    <td>{formatThousands(item.vl_Boleto)}</td>
                  ) : (
                    <td>---</td>
                  ),
                vl_Pago: (item) =>
                  item.vl_Pago ? (
                    <td>{formatThousands(item.vl_Pago)}</td>
                  ) : (
                    <td>---</td>
                  ),
                status: (item) => renderStatus(item.status),
                boleto_Emitido: (item) =>
                  item.boleto_Emitido ? (
                    <td style={{ textAlign: "center" }}>
                      <i className="cil-check-circle" />
                    </td>
                  ) : (
                    <td>X</td>
                  ),
                codigo: (item) =>
                  item.codigo ? <td>{item.vl_Boleto}</td> : <td>---</td>,
              }}
            />
          ) : (
            <CCol style={{ textAlign: "center" }}>
              <div>Não há boletos gerados para essa parcela.</div>
            </CCol>
          )}
        </div>
      ),
    },
    {
      id: 2,
      label: "Recibos",
      icon: "cil-notes",
      content: (
        <div>
          {tableRecibos ? (
            <CDataTable
              items={tableRecibos}
              fields={recibosFields}
              hover
              responsive
              scopedSlots={{
                detalhes: (item) => (
                  <td>
                    <CButton
                      className="button-link py-0 px-0"
                      onClick={() => handleDetalhesRecibo(item)}
                    >
                      Detalhes
                    </CButton>
                  </td>
                ),
                nr_Boleto: (item) => (
                  <td className="nowrap-cell">{item.nr_Boleto}</td>
                ),
                status: (item) => renderStatus(item.status),
                recibo_Sobre: (item) => renderReciboSobre(item.recibo_Sobre),
                dt_Emiss: (item) =>
                  item.dt_Emiss ? (
                    <td>{formatDate(item.dt_Emiss)}</td>
                  ) : (
                    <td>---</td>
                  ),
                dt_Pagto: (item) =>
                  item.dt_Pagto ? (
                    <td>{formatDate(item.dt_Pagto)}</td>
                  ) : (
                    <td>---</td>
                  ),
                vl_Pago: (item) =>
                  item.vl_Pago ? (
                    <td>{formatCurrency(item.vl_Pago, false)}</td>
                  ) : (
                    <td>---</td>
                  ),
                descricao_Motivo_Cancel: (item) =>
                  item.descricao_Motivo_Cancel ? (
                    <td>{item.descricao_Motivo_Cancel}</td>
                  ) : (
                    <td>---</td>
                  ),
              }}
            />
          ) : (
            <CCol style={{ textAlign: "center" }}>
              <div>Não há recibos gerados para essa parcela.</div>
            </CCol>
          )}
        </div>
      ),
    },
    {
      id: 3,
      label: "Negociações",
      icon: "cil-dollar",
      content: (
        <div>
          {tableNegociacoes ? (
            <CDataTable
              items={tableNegociacoes}
              fields={negociacaoFields}
              hover
              responsive
              scopedSlots={{
                status: (item) => renderStatus(item.status),
                liberado: (item) =>
                  item.liberado ? (
                    <td style={{ textAlign: "center" }}>
                      <i className="cil-check-circle" />
                    </td>
                  ) : (
                    <td>X</td>
                  ),
                descricao: (item) => (
                  <td className="nowrap-cell">{item.descricao}</td>
                ),
                negociacao_Enviada: (item) =>
                  item.negociacao_Enviada ? (
                    <td style={{ textAlign: "center" }}>
                      <i className="cil-check-circle" />
                    </td>
                  ) : (
                    <td style={{ textAlign: "center" }}>X</td>
                  ),
                dt_Negociacao: (item) =>
                  item.dt_Negociacao ? (
                    <td>{formatDate(item.dt_Negociacao)}</td>
                  ) : (
                    <td>---</td>
                  ),
                dt_Cadastro_Negociacao: (item) =>
                  item.dt_Cadastro_Negociacao ? (
                    <td>{formatDate(item.dt_Cadastro_Negociacao)}</td>
                  ) : (
                    <td>---</td>
                  ),
                vl_Negociacao: (item) =>
                  item.vl_Negociacao ? (
                    <td>{formatThousands(item.vl_Negociacao)}</td>
                  ) : (
                    <td>---</td>
                  ),
              }}
            />
          ) : (
            <CCol style={{ textAlign: "center" }}>
              <div>Não há negociações geradas para essa parcela.</div>
            </CCol>
          )}
        </div>
      ),
    },
  ];

  const [currentTab, setCurrentTab] = useState("Boletos");

  const renderStatus = (status) => {
    switch (status) {
      case "P":
        return <td>Pago</td>;
      case "A":
        return <td>Aberto</td>;
      case "C":
        return <td>Cancelado</td>;
      default:
        break;
    }
  };

  async function getDadosAcordo(id_Parcela_Acordo) {
    const data = {
      IdParcelaAcordo: id_Parcela_Acordo,
      numeroContrato: financiadoData.numero_Contrato,
      ...(financiadoData.idLinkedGroup &&
        financiadoData.idGrupo && {
          groupId: financiadoData.idGrupo,
          linkedGroupId: financiadoData.idLinkedGroup,
        }),
    };
    const boletos = await GET_DATA("DataCob/Acordos/Parcelas/Boleto", data);
    const recibos = await GET_DATA("DataCob/Acordos/Parcelas/Recibo", data);
    const negociacao = await GET_DATA(
      "DataCob/Acordos/Parcelas/Negociacao",
      data
    );

    if (!boletos) {
      setTableBoletos(null);
    } else {
      setTableBoletos(boletos);
      setBoletoSelecionado(boletos[0].id_Boleto);
    }

    if (!recibos) {
      setTableRecibos(null);
    } else {
      setTableRecibos(recibos);
    }

    if (!negociacao) {
      setTableNegociacoes(null);
    } else {
      setTableNegociacoes(negociacao);
    }

    setCurrentTab("Boletos");
    return;
  }

  async function getParcelas() {
    const data = {
      IdAcordo: dados[0].id_Acordo,
      numeroContrato: financiadoData.numero_Contrato,
      ...(financiadoData.idLinkedGroup &&
        financiadoData.idGrupo && {
          groupId: financiadoData.idGrupo,
          linkedGroupId: financiadoData.idLinkedGroup,
        }),
    };
    const parcelas = await GET_DATA("Datacob/Acordos/Parcelas", data);
    setDadosAcordo(parcelas);
  }

  const renderReciboSobre = (status) => {
    switch (status) {
      case "A":
        return <td>Acordo</td>;
      default:
        return <td>---</td>;
    }
  };

  const handleClose = () => {
    onClose("AbrirNegociacao");
  };

  const handleCloseSecondaryModal = () => {
    setShowModalDetalhesBoleto(false);
    setShowModalDetalhesRecibo(false);
    setShowModalCancelarBoleto(false);

    setDetalhesBoleto(null);
    setDetalhesRecibo(null);

    getParcelas();
  };

  const handleCancelarBoleto = () => {
    setShowModalCancelarBoleto(true);
  };

  const setParcelasValues = (item) => {
    setIdParcela(item.id_Parcela_Acordo);
    setNumParcela(item.nr_Parcela);
  };

  const handleDetalhesBoleto = async (item) => {
    const data = {
      IdParcelaAcordo: idParcela,
      numeroContrato: financiadoData.numero_Contrato,
      ...(financiadoData.idLinkedGroup &&
        financiadoData.idGrupo && {
          groupId: financiadoData.idGrupo,
          linkedGroupId: financiadoData.idLinkedGroup,
        }),
    };
    const detBoletos = await GET_DATA("Datacob/Acordos/Parcelas/Boleto", data);
    setDetalhesBoleto(detBoletos[0]);
    setShowModalDetalhesBoleto(true);
  };

  const handleDetalhesRecibo = async (item) => {
    const data = {
      IdParcelaAcordo: idParcela,
      numeroContrato: financiadoData.numero_Contrato,
      ...(financiadoData?.idLinkedGroup &&
        financiadoData?.idGrupo && {
          groupId: financiadoData?.idGrupo,
          linkedGroupId: financiadoData?.idLinkedGroup,
        }),
    };
    const detRecibos = await GET_DATA("Datacob/Acordos/Parcelas/Recibo", data);
    setDetalhesRecibo(detRecibos[0]);
    setShowModalDetalhesRecibo(true);
  };

  const handleVisualizarBoleto = async (item) => {
    const data = { IdBoleto: item.id_Boleto };
    const boleto = await GET_DATA("Datacob/DownloadBoleto", data);

    // Defina um título para a nova janela
    const windowTitle = "Visualização de PDF";

    // Crie uma nova janela com título
    const newWindow = window.open(
      "",
      "_blank",
      `width=800,height=600,toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,title=${windowTitle}`
    );

    // Escreva o conteúdo do PDF na nova janela
    newWindow.document.open();
    newWindow.document.write(
      '<embed width="100%" height="100%" src="data:application/pdf;base64,' +
        boleto +
        '" type="application/pdf"/>'
    );
    newWindow.document.close();
  };

  const handleUpdateModal = (sucesso) => {
    if (idParcela && sucesso) {
      getDadosAcordo(idParcela);
    }
  };

  useEffect(() => {
    if (isOpen && dados) {
      setDadosAcordo(dados);
      setIdParcela(dados[0].id_Parcela_Acordo);
      setNumParcela(dados[0].nr_Parcela);
      getDadosAcordo(dados[0].id_Parcela_Acordo);
    }
  }, [isOpen]);

  useEffect(() => {
    if (idParcela) {
      getDadosAcordo(idParcela);
    }
  }, [idParcela]);

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false} size="xl">
      <CModalHeader closeButton>Abrir Negociação - Parcelas</CModalHeader>
      <CModalBody>
        <div style={{ maxHeight: "300px", overflowX: "auto" }}>
          <div style={{ width: "fit-content", overflowY: "auto" }}>
            <CDataTable
              items={dadosAcordo}
              fields={parcelasColumns}
              hover
              responsive
              scopedSlots={{
                nr_Parcela: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    {item.nr_Parcela}
                  </td>
                ),
                nr_Plano: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    {item.nr_Plano}
                  </td>
                ),
                atraso: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                  >
                    {item.atraso}
                  </td>
                ),
                dt_Vencimento: (item) =>
                  item.dt_Vencimento ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatDate(item.dt_Vencimento)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                dt_Pagamento: (item) =>
                  item.dt_Pagamento ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatDate(item.dt_Pagamento)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                dt_Venc_Boleto: (item) =>
                  item.dt_Venc_Boleto ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatDate(item.dt_Venc_Boleto)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                dt_Atualizacao: (item) =>
                  item.dt_Atualizacao ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatDate(item.dt_Atualizacao)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                vl_Saldo: (item) =>
                  item.vl_Saldo ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatThousands(item.vl_Saldo)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                vl_Atualizado: (item) =>
                  item.vl_Atualizado ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatThousands(item.vl_Atualizado)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                vl_Pago: (item) =>
                  item.vl_Pago ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatThousands(item.vl_Pago)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                vl_Honor: (item) =>
                  item.vl_Honor ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {formatThousands(item.vl_Honor)}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                nr_Boleto: (item) =>
                  item.nr_Boleto ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {item.nr_Boleto}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                url_Boleto: (item) =>
                  item.url_Boleto ? (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      {item.url_Boleto}
                    </td>
                  ) : (
                    <td
                      style={{ cursor: "pointer" }}
                      onClick={() => setParcelasValues(item)}
                    >
                      ---
                    </td>
                  ),
                linha_Digitavel: (item) => (
                  <td
                    style={{ cursor: "pointer" }}
                    onClick={() => setParcelasValues(item)}
                    className="nowrap-cell"
                  >
                    {item.linha_Digitavel}
                  </td>
                ),
              }}
            />
          </div>
        </div>
        <CCard className="mt-4">
          <CTabs activeTab={currentTab}>
            <CRow>
              <CCol md="1" className="pr-0">
                <CNav variant="tabs" className="flex-column">
                  {tabs.map((tab) => (
                    <CNavItem key={tab.id}>
                      <CTooltip content={tab.label}>
                        <CNavLink
                          className="d-flex justify-content-center"
                          data-tab={tab.label}
                          onClick={() => setCurrentTab(tab.label)}
                        >
                          <i className={tab.icon} />
                        </CNavLink>
                      </CTooltip>
                    </CNavItem>
                  ))}
                </CNav>
              </CCol>
              <CCol
                className="pl-0"
                style={{ maxHeight: "250px", overflowX: "auto" }}
              >
                <CTabContent className="overflow-auto">
                  {tabs.map((tab) => (
                    <CTabPane key={tab.id} data-tab={tab.label}>
                      {tab.content}
                    </CTabPane>
                  ))}
                </CTabContent>
              </CCol>
            </CRow>
          </CTabs>
        </CCard>
      </CModalBody>
      <CModalFooter>
        {currentTab === "Boletos" && (
          <>
            {/* <CButton color="secondary" onClick={handleClose} disabled>
                Reenviar Boleto
            </CButton> */}
            <CButton
              color={tableBoletos ? "danger" : "secondary"}
              onClick={handleCancelarBoleto}
              title={inforPermissions(permissaoNegociacaoBoleto).delete}
              disabled={
                !tableBoletos ||
                !checkPermission(
                  permissaoNegociacaoBoleto.modulo,
                  "Delete",
                  permissaoNegociacaoBoleto.submodulo
                )
              }
            >
              Cancelar Boleto
            </CButton>
          </>
        )}
        <CButton color="secondary" onClick={handleClose}>
          Fechar
        </CButton>
      </CModalFooter>
      <DetalhesBoletoModal
        isOpen={showModalDetalhesBoleto}
        onClose={handleCloseSecondaryModal}
        dados={detalhesBoleto}
        parcela={numParcela}
        updateModal={handleUpdateModal}
      />
      <DetalhesReciboModal
        isOpen={showModalDetalhesRecibo}
        onClose={handleCloseSecondaryModal}
        dados={detalhesRecibo}
      />
      <CancelarBoletoModal
        isOpen={showModalCancelarBoleto}
        onClose={handleCloseSecondaryModal}
        boleto={boletoSelecionado}
        onSubmit={handleUpdateModal}
      />
    </CModal>
  );
};

export default AbrirNegociacaoModal;
