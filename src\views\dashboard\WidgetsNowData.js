import React, { useState, useEffect } from "react";
import { CWidgetDropdown, CRow, CCol, CDropdown } from "@coreui/react";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

import ChartLineSimple from "../charts/ChartLineSimple";

const WidgetsNowData = ({ filter }) => {

  const [contractData, setContractData] = useState([{ total: 0, dataset: [] }]);
  const [loading, setLoading] = useState(false);

  const getDados = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const Color = (crm) => {
    return { Rodobens: "info", GVC: "primary" }[crm] || "gradient-primary";
  };

  async function agruparDados(data) {
    const groupedMap = new Map();
    const hours = new Map();

    data?.forEach(item => {
      const key = `${item.crm}`;
      const hour = new Date(item.dt_Contr).getHours().toString().padStart(2, '0');

      if (!hours.has(key)) hours.set(key,{hour:[]});
      if (!hours.get(key).hour.find(a => a === hour)) hours.get(key).hour.push(hour);

      if (!groupedMap.has(key)) {
        groupedMap.set(key, {
          key,
          crm: item.crm,
          text: `${item.crm} Inseridos hoje`,
          color: `gradient-${Color(item.crm)}`,
          backgroundColor: `${Color(item.crm)}`,
          total: 0,
          dataSet: [],
        });
      }
      const index = hours.get(key).hour.indexOf(hour);
      const group = groupedMap.get(key);
      group.total += item.count;
      group.dataSet[index] = (group.dataSet[index] ?? 0) + item.count;
    });

    return Array.from(groupedMap.values());
  }

  async function defaultDatasets(filterActive) {
    try {
      const payload = {
        crm: filterActive?.crm ?? "",
        groupId: filterActive?.groupId ?? "",
        startDate: new Date().toISOString().substring(0, 10),
        endDate: new Date().toISOString().substring(0, 10)
      };

      const data = await getDados(payload, "getDashbordContract");
      if (!data) return [{ total: 0, dataset: [] }];

      return await agruparDados(data);
    } catch (err) {
      console.error(err);
      return [{ total: 0, dataset: [] }];
    }
  }

  useEffect(() => {
    setLoading(true);
    defaultDatasets(filter).then((result) => {
      setContractData(result);
      setLoading(false);
    });
  }, [filter]);


  if (loading) return <div>Carregando...</div>;
  if (!contractData.length || !contractData[0]?.total) return <div>Nenhum dado para exibir.</div>;

  return (
    <CRow>
      {contractData.map((crm) => (
        <CCol sm="6" lg="3" key={crm.key}>
          <CWidgetDropdown
            color={crm.color}
            header={crm.total}
            text={crm.text}
            footerSlot={
              <ChartLineSimple
                pointed
                className="c-chart-wrapper mt-3 mx-3"
                style={{ height: "70px" }}
                dataPoints={Array.from(crm.dataSet)}
                pointHoverBackgroundColor={crm.backgroundColor}
                label="Contratos"
                labels="Horas"
              />
            }
          >
            <CDropdown>
              {/*
            <CDropdownToggle color="transparent">
              <CIcon name="cil-settings" />
            </CDropdownToggle>
            <CDropdownMenu className="pt-0" placement="bottom-end">
              <CDropdownItem>Action</CDropdownItem>
              <CDropdownItem>Another action</CDropdownItem>
              <CDropdownItem>Something else here...</CDropdownItem>
              <CDropdownItem disabled>Disabled action</CDropdownItem>
            </CDropdownMenu>*/}
            </CDropdown>
          </CWidgetDropdown>
        </CCol>
      ))}
      {/*
      <CCol sm="6" lg="3">
        <CWidgetDropdown
          color="gradient-info"
          header="9.823"
          text="Usuários online"
          footerSlot={
            <ChartLineSimple
              pointed
              className="mt-3 mx-3"
              style={{ height: "70px" }}
              dataPoints={[1, 18, 9, 17, 34, 22, 11]}
              pointHoverBackgroundColor="info"
              options={{ elements: { line: { tension: 0.00001 } } }}
              label="Members"
              labels="months"
            />
          }
        >
          <CDropdown>            
            <CDropdownToggle caret={false} color="transparent">
              <CIcon name="cil-location-pin" />
            </CDropdownToggle>
            <CDropdownMenu className="pt-0" placement="bottom-end">
              <CDropdownItem>Action</CDropdownItem>
              <CDropdownItem>Another action</CDropdownItem>
              <CDropdownItem>Something else here...</CDropdownItem>
              <CDropdownItem disabled>Disabled action</CDropdownItem>
            </CDropdownMenu>
          </CDropdown>
        </CWidgetDropdown>
      </CCol>*/}

      {/*
      <CCol sm="6" lg="3">
        <CWidgetDropdown
          color="gradient-danger"
          header="9.823"
          text="Members online"
          footerSlot={
            <ChartBarSimple
              className="mt-3 mx-3"
              style={{ height: "70px" }}
              backgroundColor="rgb(250, 152, 152)"
              label="Members"
              labels="months"
            />
          }
        >
          <CDropdown>
            <CDropdownToggle caret className="text-white" color="transparent">
              <CIcon name="cil-settings" />
            </CDropdownToggle>
            <CDropdownMenu className="pt-0" placement="bottom-end">
              <CDropdownItem>Action</CDropdownItem>
              <CDropdownItem>Another action</CDropdownItem>
              <CDropdownItem>Something else here...</CDropdownItem>
              <CDropdownItem disabled>Disabled action</CDropdownItem>
            </CDropdownMenu>
          </CDropdown>
        </CWidgetDropdown>
      </CCol>*/}
    </CRow>
  );
};

export default WidgetsNowData;
