import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  ReactNode,
} from "react";
import { fetchAutoNegociacao } from "./ApiAutoNegociacaoConfig.js";

export interface Contract {
  contractCode: number;
  groupId: number;
  groupingId: number;
  financedName: string;
  actualDebtAmount: number;
  maxOverdueDays: number;
  negotiationAmount?: number;
  negotiationDate?: string;
  status?: string;
  interestCategory?: string;
  abandonmentReason?: string;
  isConverted: boolean;
  createdAt: string;
  updatedAt?: string;
}

export interface Client {
  id?: string;
  document: string;
  name: string;
  primaryEmail: string;
  emails: string[];
  primaryPhone: string;
  phones: string[];
  status: string;
  createdAt: string;
  updatedAt?: string;
  contracts: Contract[];
}

interface AutoNegociacaoContextType {
  clients: Client[];
}

const AutoNegociacaoContext = createContext<
  AutoNegociacaoContextType | undefined
>(undefined);

export const AutoNegociacaoProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  const [clients, setClients] = useState<Client[]>([]);

  const fetchClients = async () => {
    try {
      const response = await fetchAutoNegociacao({
        endpointKey: "getClientsWithContracts",
        method: "GET",
      });
      setClients(Array.isArray(response.data) ? response.data : []);
    } catch (error) {
      console.error("Erro ao buscar clients do Portal Auto Negociação:", error);
    }
  };

  useEffect(() => {
    fetchClients();
  }, []);

  return (
    <AutoNegociacaoContext.Provider value={{ clients }}>
      {children}
    </AutoNegociacaoContext.Provider>
  );
};

export const useAutoNegociacao = () => {
  const context = useContext(AutoNegociacaoContext);
  if (!context)
    throw new Error(
      "useAutoNegociacao deve ser usado dentro de AutoNegociacaoProvider"
    );
  return context;
};
