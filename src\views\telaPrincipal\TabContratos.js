import React, { useState, useEffect } from "react";
import {
  CCardBody,
  CCol,
  CRow,
  CTabs,
  CTabPane,
  CNav,
  CNavItem,
  CNavLink,
  CCard,
  CLabel,
  CTabContent,
} from "@coreui/react";
import BtnBoletoManual from "./CardContratos/Parcial/BtnBoletoManual";
import TabProcessos from "./TabProcessos";
import TabEnvioEmails from "./TabEnvioEmails";

import DadosContratos from "./CardContratos/Contratos";
import Financiado from "./CardContratos/Financiado";
import CotasAtraso from "./CardContratos/CotasAtraso";
import { useAuth } from "src/auth/AuthContext";
import { useMyContext } from "src/reusable/DataContext";
import { getProcessos, getApi, getEntidades } from "src/reusable/functions";
import LoadingComponent from "src/reusable/Loading";
import OccurrenceIndicatorModal from "./Modal/OccurrenceIndicatorModal.tsx";
import Boletos from "./CardContratos/Boletos.js";
import EnderecoLista from "./CardContratos/EnderecoLista.js";
import DadosAuxiliares from "./CardContratos/DadosAuxiliares.js";
import PixTab from "./CardContratos/PixTab.js";

const COR_JURIDICO_STD = "#95e0f0";

const CardContratos = ({
  pasta,
  onHandleAtualizarCards,
  onLoadProcessos,
  onLoadNewcon,
}) => {
  const {
    data,
    processos,
    updateProcessos,
    updateAppSettings,
    appSettings,
    updateCustasProjuris,
  } = useMyContext();
  const { checkPermission } = useAuth();
  const permissaoJuridico = {
    modulo: "Jurídico CRM",
    submodulo: "Jurídico",
  };
  const permissaoTelaPrincipal = {
    modulo: "Tela Principal",
    submodulo: null,
  };

  const permissaoTelaPrincipalFinanciado = {
    modulo: "Tela Principal",
    submodulo: "Financiado",
  };
  const permissaoTelaPrincipalEndereco = {
    modulo: "Tela Principal",
    submodulo: "Endereço",
  };
  const permissaoTelaPrincipalTelefone = {
    modulo: "Tela Principal",
    submodulo: "Telefone",
  };

  const permissaoTelaPrincipalEmail = {
    modulo: "Tela Principal",
    submodulo: "Email",
  };

  const permissaoTelaPrincipalBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  };

  const permissaoTelaPrincipalContratosAtivos = {
    modulo: "Tela Principal",
    submodulo: "Contratos Ativos",
  };

  const [currentTab, setCurrentTab] = useState("Contratos");
  const [selectedTabIndex, setSelectedTabIndex] = useState("Contratos");
  const [mensagemAlerta, setMensagemAlerta] = useState([]);

  const [modalIndicatorOccurrence, setModalIndicatorOccurrence] =
    useState(false);
  const [isProcessoLoading, setIsProcessoLoading] = useState(false);
  const [docData, setDocData] = useState([]);
  const [temProcessos, setTemProcessos] = useState(false);
  const [showLegalStatus, setShowLegalStatus] = useState(false);

  const [nrFaseBusca, setNrFaseBusca] = useState(0);
  const [nrPastas, setNrPastas] = useState(0);
  const [showAlertJuri, setShowAlertJuri] = useState(false);
  const [arrayColorIndicatorJur, setArrayColorIndicatorJur] = useState([]);
  const [colorIndicatorJur, setColorIndicatorJur] = useState(COR_JURIDICO_STD);

  const cotasAbertas = localStorage.getItem("contratosAbertos")
    ? JSON.parse(localStorage.getItem("contratosAbertos"))
    : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const handleTabSelect = (tab) => {
    setCurrentTab(tab.label);
    setSelectedTabIndex(tab.id);
  };

  const getCustasProjuris = async (processos) => {
    if (processos && processos.length > 0) {
      try {
        let custas = [];
        await processos.forEach(async (element) => {
          const response = await getApi(
            { Pasta: element.idProcesso },
            "getcustasprojuris"
          );
          if (response && response.length > 0) {
            custas.push(...response);
            updateCustasProjuris([...custas]);
          }
        });
      } catch (error) {}
    } else {
      updateCustasProjuris(null);
    }
  };

  const filterTabsPermissions = () => {
    return tabs.filter((tab) => {
      if (tab.id === "Contratos")
        return checkPermission(
          permissaoTelaPrincipal.modulo,
          "View",
          permissaoTelaPrincipal.submodulo
        );
      if (tab.id === "DadosAuxiliares") return true;
      if (tab.id === "Financiado")
        return checkPermission(
          permissaoTelaPrincipalFinanciado.modulo,
          "View",
          permissaoTelaPrincipalFinanciado.submodulo
        );
      if (tab.id === "Endereço")
        return checkPermission(
          permissaoTelaPrincipalEndereco.modulo,
          "View",
          permissaoTelaPrincipalEndereco.submodulo
        );
      if (tab.id === "Telefone")
        return checkPermission(
          permissaoTelaPrincipalTelefone.modulo,
          "View",
          permissaoTelaPrincipalTelefone.submodulo
        );
      if (tab.id === "E-mail")
        return checkPermission(
          permissaoTelaPrincipalEmail.modulo,
          "View",
          permissaoTelaPrincipalEmail.submodulo
        );
      if (tab.id === "Jurídico")
        return checkPermission(
          permissaoJuridico.modulo,
          "View",
          permissaoJuridico.submodulo
        );
      if (tab.id === "Cotas em atraso")
        return checkPermission(
          permissaoTelaPrincipalContratosAtivos.modulo,
          "View",
          permissaoTelaPrincipalContratosAtivos.submodulo
        );
      if (tab.id === "Boletos")
        return checkPermission(
          permissaoTelaPrincipalBoleto.modulo,
          "View",
          permissaoTelaPrincipalBoleto.submodulo
        );
      if (tab.id === "Pix")
        return checkPermission(
          permissaoTelaPrincipalBoleto.modulo,
          "View",
          permissaoTelaPrincipalBoleto.submodulo
        );

      if (tab.id === "EnvioEmail") return true;
      return false;
    });
  };

  const tabs = [
    {
      id: "Contratos",
      label: "Contratos",
      icon: "cil-file",
      content: (
        <DadosContratos
          onLoadNewcon={onLoadNewcon}
          selected={selectedTabIndex === "Contratos"}
          nrFaseBusca={nrFaseBusca}
          showAlertJuri={showAlertJuri}
        />
      ),
    },
    {
      id: "DadosAuxiliares",
      label: "Dados Auxiliares",
      icon: "cil-file",
      content: <DadosAuxiliares />,
    },
    {
      id: "Financiado",
      label: "Financiado",
      icon: "cil-user",
      content: <Financiado />,
    },
    {
      id: "Endereço",
      label: "Endereço",
      icon: "cil-location-pin",
      content: <EnderecoLista />,
    },
    {
      id: "Boletos",
      label: "Boletos",
      icon: "cil-barcode",
      content: <Boletos selected={selectedTabIndex === "Boletos"} />,
    },
    {
      id: "Pix",
      label: "Pix",
      icon: "cil-qrcode",
      content: <PixTab selected={selectedTabIndex === "Pix"} />,
    },
    {
      id: "EnvioEmail",
      label: "Envio de Email",
      icon: "cil-envelope-open",
      content: <TabEnvioEmails active={selectedTabIndex === "EnvioEmail"} />,
    },
    appSettings?.juridico?.showProcessos
      ? {
          id: "Jurídico",
          label: "Jurídico",
          icon: "cil-inbox",
          content: (
            <TabProcessos
              onPastaChange={(pasta) => {
                setDocData(
                  processos.find((processo) => processo.pasta === pasta)
                );
              }}
            />
          ),
        }
      : {},
    cotasAbertas
      ? {
          id: "Cotas em atraso",
          label: "Cotas em atraso",
          icon: "cil-warning",
          content: <CotasAtraso />,
        }
      : [],
  ];

  useEffect(() => {
    if (processos && processos.length > 0) {
      setDocData(processos.find((processo) => processo.pasta === pasta));
    }
  }, [pasta]);

  useEffect(() => {
    const contratosAbertos = localStorage.getItem("contratosAbertos")
      ? JSON.parse(localStorage.getItem("contratosAbertos"))
      : null;
    if (
      contratosAbertos &&
      contratosAbertos !== undefined &&
      contratosAbertos.length > 0
    ) {
      setMensagemAlerta(`Há mais ${contratosAbertos.length}
    contratos em aberto. Clique para visualizar os ${contratosAbertos.length} contratos.`);
    } else {
      setCurrentTab("Contratos");
      setSelectedTabIndex("Contratos");
    }

    const processos = localStorage.getItem("processos")
      ? JSON.parse(localStorage.getItem("processos"))
      : null;
    if (processos && processos.length > 0) {
      setTemProcessos(true);
    } else {
      setTemProcessos(false);
    }
    setDocData([]);
    setShowLegalStatus(false);
    setShowAlertJuri(false);
    setNrFaseBusca(0);
    getIndicatorOccurrence();
    getColorIndicatorJur();
  }, [data]);

  useEffect(() => {
    setShowLegalStatus(false);
    updateViewProcessos(false).then(() => {
      setShowLegalStatus(true);
    });
  }, [data]);

  const getIndicatorOccurrence = async () => {
    if (!data?.id_Agrupamento) return;
    const response = await getApi(
      { idAgrupamento: data.id_Agrupamento },
      "getIndicadoresHistoricos"
    );
    if (response && response.length > 0) {
      const fil = response.filter(
        (item) => item.descricao === "VEICULO LOCALIZADO"
      );
      setShowAlertJuri(fil.length > 0);
    } else {
      setShowAlertJuri(false);
    }
  };

  const getColorIndicatorJur = async () => {
    if (!data?.id_Agrupamento) return;
    const response = await getApi(null, "restCorJuridicoParam");
    if (response && response.length > 0) {
      setArrayColorIndicatorJur(response);
    } else {
      setArrayColorIndicatorJur([]);
    }
  };

  const updateViewProcessos = async (reload = true) => {
    if (!data?.cpfCnpj) return;
    setIsProcessoLoading(true);
    // const process = localStorage.getItem("processos")
    //   ? JSON.parse(localStorage.getItem("processos"))
    //   : null;
    const process = processos;
    if (reload !== true && process !== null) {
      if (process && process.length > 0) {
        const procs = process.filter(
          (p) => p.status === "ATIVOS" || p.status === "BAIXA PROVISÓRIA"
        );
        if (procs.length > 0) {
          setTemProcessos(true);
          setDocData(procs[0]);
          updateProcessos(procs);
          appSettings.juridico.showProcessos = true;
          // getCustasProjuris(procs);
          onLoadProcessos();
          const nrFase = procs.filter(
            (processo) => processo.tipo_de_Acao === "Busca e Apreensão"
          );
          setNrFaseBusca(nrFase.length);
          setNrPastas(procs.length);
          getIndicatorOccurrence();
          if (arrayColorIndicatorJur.length > 0) {
            const color = arrayColorIndicatorJur.find(
              (item) => item.name === procs[0].fase_Atual
            );
            setColorIndicatorJur(
              color !== null && color !== undefined
                ? color.colorHexa
                : COR_JURIDICO_STD
            );
          }
        } else {
          handleSemProcessos();
        }
      } else {
        handleSemProcessos();
      }
      setIsProcessoLoading(false);
      return;
    }

    await getEntidades(data?.cpfCnpj).then((entidades) => {
      if ((entidades && entidades.length === 0) || entidades === undefined) {
        setTemProcessos(false);
        if (appSettings?.juridico) appSettings.juridico.showProcessos = false;
        updateAppSettings(appSettings);
        setIsProcessoLoading(false);
        return;
      }

      if (entidades) {
        getProcessos(entidades[0].idEntidade)
          .then((processos) => {
            if (processos && processos.length > 0) {
              const procs = processos.filter(
                (p) => p.status === "ATIVOS" || p.status === "BAIXA PROVISÓRIA"
              );
              if (procs.length > 0) {
                setTemProcessos(true);
                setDocData(procs[0]);
                updateProcessos(procs);
                appSettings.juridico.showProcessos = true;
                getCustasProjuris(procs);
                onLoadProcessos();
                const nrFase = procs.filter(
                  (processo) => processo.tipo_de_Acao === "Busca e Apreensão"
                );
                setNrFaseBusca(nrFase.length);
                setNrPastas(procs.length);
                getIndicatorOccurrence();
                if (arrayColorIndicatorJur.length > 0) {
                  const color = arrayColorIndicatorJur.find(
                    (item) => item.name === procs[0].fase_Atual
                  );
                  setColorIndicatorJur(
                    color !== null && color !== undefined
                      ? color.colorHexa
                      : COR_JURIDICO_STD
                  );
                }
              } else {
                handleSemProcessos();
              }
            } else {
              handleSemProcessos();
            }
            updateAppSettings(appSettings);
          })
          .catch((err) => {
            setTemProcessos(false);
            appSettings.juridico.showProcessos = false;
            updateAppSettings(appSettings);
            console.log(err);
          })
          .finally(() => {
            setIsProcessoLoading(false);
          });
      }
    });
  };

  const handleSemProcessos = () => {
    setTemProcessos(false);
    appSettings.juridico.showProcessos = false;
    setNrFaseBusca(0);
    setNrPastas(0);
    setColorIndicatorJur(COR_JURIDICO_STD);
  };

  const handleAtualizarCards = () => {
    console.warn("Atualizar Cards - HandleSave");
    if (onHandleAtualizarCards) onHandleAtualizarCards();
  };

  const filteredTabs = filterTabsPermissions().filter((tab) => tab.content);

  return (
    <>
      <CRow>
        <CCol md={currentTab === "Contratos" ? "10" : "12"} xs="12">
          <CCard style={{ minHeight: "250px", overflow: "auto" }}>
            <CTabs activeTab={currentTab}>
              <div className="d-flex justify-content-between">
                <CNav className="custom-nav">
                  {filteredTabs.map((tab) => (
                    <CNavItem
                      key={tab.id}
                      className={
                        currentTab === tab.label
                          ? ""
                          : tab.label === "Cotas em atraso"
                          ? "cotasatraso-tab"
                          : "nonactive-tab"
                      }
                    >
                      <CNavLink
                        data-tab={tab.label}
                        onClick={() => handleTabSelect(tab)}
                      >
                        <i className={tab.icon} /> {tab.label}
                      </CNavLink>
                    </CNavItem>
                  ))}
                </CNav>
                <div>
                  <span
                    style={{
                      fontSize: "1.5rem",
                      marginRight: "10px",
                      cursor: "pointer",
                    }}
                    onClick={() => setModalIndicatorOccurrence(true)}
                  >
                    <i className={"cil-warning"} />
                  </span>
                </div>
              </div>
              <CTabContent>
                {filteredTabs.map((tab) => (
                  <CTabPane key={tab.id} data-tab={tab.label}>
                    {tab.content}
                  </CTabPane>
                ))}
              </CTabContent>
            </CTabs>
          </CCard>
        </CCol>

        {currentTab === "Contratos" && (
          <>
            <CCol md="2" xs="6">
              <CRow>
                <CCol>
                  {checkPermission(
                    permissaoJuridico.modulo,
                    "View",
                    permissaoJuridico.submodulo
                  ) &&
                    financiadoData && (
                      // pasta &&
                      // docData &&
                      // pasta !== undefined &&
                      // pasta !== "" && (
                      <>
                        {!showLegalStatus && (
                          <button
                            style={{ width: "100%" }}
                            className="btn btn-warning text-white mb-2"
                            onClick={() => {
                              updateViewProcessos();
                              setShowLegalStatus(true);
                            }}
                          >
                            Mostrar Situação Jurídica
                          </button>
                        )}
                        {showLegalStatus && (
                          <>
                            <CCard
                              style={{
                                backgroundColor: colorIndicatorJur,
                                color: "black",
                                minHeight: "120px",
                                marginBottom: "5px",
                              }}
                            >
                              <CCardBody
                                style={{
                                  display: "flex",
                                  flexDirection: "column",
                                }}
                              >
                                {isProcessoLoading ? (
                                  <LoadingComponent
                                    text={
                                      "Aguarde! Buscando dados no Projuris! Isso pode demorar um pouco."
                                    }
                                  />
                                ) : (
                                  <>
                                    {temProcessos ? (
                                      <>
                                        <CLabel
                                          color="dark"
                                          style={{ fontSize: "small" }}
                                        >
                                          Status Jurídico:{" "}
                                          <strong>{docData.status}</strong>
                                        </CLabel>
                                        <CLabel
                                          color="dark"
                                          style={{ fontSize: "small" }}
                                        >
                                          Fase Atual:{" "}
                                          <strong>{docData.fase_Atual}</strong>
                                        </CLabel>
                                        <CLabel
                                          color="dark"
                                          style={{
                                            fontSize: "small",
                                            color: "red",
                                          }}
                                        >
                                          <strong>
                                            Atenção! Existem {nrPastas} pastas
                                            para este cliente
                                          </strong>
                                        </CLabel>
                                        {/* <CLabel
                                      color="dark"
                                      style={{ fontSize: "small" }}
                                    >
                                      <strong>
                                        <Ancora
                                          style={{ cursor: "pointer" }}
                                          to="projuris"
                                          smooth={true}
                                          duration={500}
                                        >
                                          Clique mais detalhes
                                        </Ancora>
                                      </strong>
                                    </CLabel> */}
                                      </>
                                    ) : (
                                      <CLabel>
                                        Não existem processos jurídicos!
                                      </CLabel>
                                    )}
                                  </>
                                )}
                              </CCardBody>
                            </CCard>

                            {!isProcessoLoading && (
                              <button
                                style={{ width: "100%" }}
                                className="btn btn-info"
                                onClick={() => {
                                  updateViewProcessos();
                                }}
                              >
                                Fazer nova busca no Projuris
                              </button>
                            )}
                          </>
                        )}

                        <BtnBoletoManual />
                      </>
                    )}
                </CCol>
              </CRow>

              <CRow>
                <CCol>
                  {checkPermission(
                    permissaoTelaPrincipalFinanciado.modulo,
                    "View",
                    permissaoTelaPrincipalFinanciado.submodulo
                  ) &&
                    cotasAbertas &&
                    cotasAbertas !== undefined &&
                    cotasAbertas.length > 0 && (
                      <CCard
                        onClick={() => setCurrentTab("Cotas em atraso")}
                        style={{
                          backgroundColor: "#ff4343",
                          color: "white",
                          height: "112px",
                          cursor: "pointer",
                          marginTop: "5px",
                        }}
                      >
                        <CCardBody
                          style={{ display: "flex", flexDirection: "column" }}
                        >
                          <CLabel
                            color="dark"
                            style={{ fontSize: "small", cursor: "pointer" }}
                          >
                            {mensagemAlerta}
                          </CLabel>{" "}
                        </CCardBody>
                      </CCard>
                    )}
                </CCol>
              </CRow>
            </CCol>
          </>
        )}
      </CRow>
      {modalIndicatorOccurrence && (
        <OccurrenceIndicatorModal
          onClose={() => setModalIndicatorOccurrence(false)}
        />
      )}
    </>
  );
};

export default CardContratos;
