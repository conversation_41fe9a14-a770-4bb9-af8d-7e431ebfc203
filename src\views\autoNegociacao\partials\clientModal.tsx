import React from "react";
import {
  CModal,
  CModal<PERSON>eader,
  CModalBody,
  CModalFooter,
  CButton,
  CCard,
  CCardBody,
  CCardHeader,
  CBadge,
} from "@coreui/react";
import {
  formatDateTime,
  formatCurrency,
  formatPhone,
  formatCpfCnpj,
} from "../utils/helpers.tsx";

interface ClientModalProps {
  client: any;
  visible: boolean;
  onClose: () => void;
}

const ClientModal: React.FC<ClientModalProps> = ({
  client,
  visible,
  onClose,
}) => {
  if (!client) return null;

  return (
    <CModal show={visible} onClose={onClose} size="lg">
      <CModalHeader closeButton>Detalhes do Cliente</CModalHeader>
      <CModalBody>
        <CCard className="mb-3">
          <CCardHeader>Dados do Cliente</CCardHeader>
          <CCardBody>
            <p>
              <strong>ID:</strong> {client.id || "-"}
            </p>
            <p>
              <strong>Documento:</strong> {formatCpfCnpj(client.document)}
            </p>
            <p>
              <strong>Nome:</strong> {client.name}
            </p>
            <p>
              <strong>Email principal:</strong> {client.primaryEmail}
            </p>
            <p>
              <strong>Outros e-mails:</strong> {client.emails.join(", ") || "-"}
            </p>
            <p>
              <strong>Telefone principal:</strong>{" "}
              {formatPhone(client.primaryPhone)}
            </p>
            <p>
              <strong>Outros telefones:</strong>{" "}
              {client.phones.join(", ") || "-"}
            </p>
            <p>
              <strong>Status:</strong>{" "}
              <CBadge color="info">{client.status}</CBadge>
            </p>
            <p>
              <strong>Data de Cadastro:</strong>{" "}
              {formatDateTime(client.createdAt)}
            </p>
            <p>
              <strong>Última Atualização:</strong>{" "}
              {formatDateTime(client.updatedAt || "")}
            </p>
          </CCardBody>
        </CCard>

        <CCard>
          <CCardHeader>Contratos ({client.contracts.length})</CCardHeader>
          <CCardBody>
            {client.contracts.length === 0 ? (
              <p>Nenhum contrato vinculado.</p>
            ) : (
              client.contracts.map((contract, index) => (
                <div
                  key={index}
                  style={{
                    marginBottom: "1rem",
                    paddingBottom: "0.5rem",
                    borderBottom: "1px solid #e4e5e6",
                  }}
                >
                  <p>
                    <strong>Código do Contrato:</strong> {contract.contractCode}
                  </p>
                  <p>
                    <strong>Grupo:</strong> {contract.groupId}
                  </p>
                  <p>
                    <strong>Agrupamento:</strong> {contract.groupingId}
                  </p>
                  <p>
                    <strong>Nome do Financiado:</strong> {contract.financedName}
                  </p>
                  <p>
                    <strong>Valor Atualizado:</strong>{" "}
                    {formatCurrency(contract.actualDebtAmount)}
                  </p>
                  <p>
                    <strong>Valor Negociado:</strong>{" "}
                    {contract.negotiationAmount
                      ? formatCurrency(contract.negotiationAmount)
                      : "-"}
                  </p>
                  <p>
                    <strong>Dias em atraso:</strong> {contract.maxOverdueDays}
                  </p>
                  <p>
                    <strong>Data de Negociação:</strong>{" "}
                    {contract.negotiationDate
                      ? formatDateTime(contract.negotiationDate)
                      : "-"}
                  </p>
                  <p>
                    <strong>Status:</strong>{" "}
                    <CBadge color="secondary">{contract.status || "-"}</CBadge>
                  </p>
                  <p>
                    <strong>Interesse:</strong>{" "}
                    {contract.interestCategory || "-"}
                  </p>
                  <p>
                    <strong>Abandonado:</strong>{" "}
                    {contract.abandonmentReason || "-"}
                  </p>
                  <p>
                    <strong>Convertido:</strong>{" "}
                    {contract.isConverted ? "Sim" : "Não"}
                  </p>
                  <p>
                    <strong>Criado em:</strong>{" "}
                    {formatDateTime(contract.createdAt)}
                  </p>
                  <p>
                    <strong>Atualizado em:</strong>{" "}
                    {formatDateTime(contract.updatedAt || "")}
                  </p>
                </div>
              ))
            )}
          </CCardBody>
        </CCard>
      </CModalBody>

      <CModalFooter>
        <CButton color="secondary" onClick={onClose}>
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};
export default ClientModal;
