import React, { useCallback, useEffect, useState } from "react";
import {
  CButton,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
} from "@coreui/react";
import {
  getApi,
  getApiInline,
  postApiQueryFile,
  putApi,
} from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import { ItemFilaAprovacaoType } from "../../types/ItemFilaAprovacaoType";
import { CartaDiluicaoInfosType } from "../../types/CartaDiluicaoTypes";

interface Props {
  isOpen: boolean;
  item: ItemFilaAprovacaoType | null;
  onClose: (updateData?: boolean) => void;
}

const EditCartaDiluicaoModal = ({ isOpen, onClose, item }: Props) => {
  const handleClose = () => {
    onClose();
  };

  const [loading, setLoading] = useState(false);

  // Form fields based on CartaDiluicaoModal
  const [idInfo, setIdInfo] = useState(null);
  const [grupoCotaContrato, setGrupoCotaContrato] = useState("");
  const [clientePrincipal, setClientePrincipal] = useState("");
  const [adversoPrincipal, setAdversoPrincipal] = useState("");
  const [mesesParcelas, setMesesParcelas] = useState("");
  const [dataRetornoPagamento, setDataRetornoPagamento] = useState("");

  const loadExistingTermData = useCallback(async () => {
    if (!item?.id) return;

    try {
      const response: CartaDiluicaoInfosType = await getApiInline(
        item.id,
        "getCartaDiluicaoInfos"
      );
      if (response) {
        setIdInfo(response.id);
        setClientePrincipal(response.clientePrincipal || "");
        setAdversoPrincipal(response.adversoPrincipal || "");
        setGrupoCotaContrato(response.grupoCotaContrato || "");
        setDataRetornoPagamento(response.dataRetornoPagamento || "");
        setMesesParcelas(response.mesesParcelas || "");
      }
    } catch (error) {
      console.error("Could not load existing term data:", error);
      // This is expected if the endpoint doesn't exist yet
    }
  }, [item?.id]);

  const asyncLoadFunc = useCallback(async () => {
    setLoading(true);
    await Promise.all([loadExistingTermData()]);
    setLoading(false);
  }, [loadExistingTermData]);

  useEffect(() => {
    asyncLoadFunc();
  }, [asyncLoadFunc]);

  const handleSave = async () => {
    try {
      setLoading(true);

      const payload = {
        id: idInfo,
        clientePrincipal: clientePrincipal,
        adversoPrincipal: adversoPrincipal,
        grupoCotaContrato: grupoCotaContrato,
        dataRetornoPagamento: dataRetornoPagamento,
        mesesParcelas: mesesParcelas,
      };

      const response = await putApi(payload, "putCartaDiluicaoInfos");

      if (response?.success === true) {
        toast.success("Carta atualizado com sucesso!");
        onClose(true);
      } else {
        toast.error("Erro ao atualizar carta!");
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao atualizar carta!");
    } finally {
      setLoading(false);
    }
  };

  const [previewLoading, setPreviewLoading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState(null);
  const [showPreview, setShowPreview] = useState(false);
  const handlePreview = async () => {
    setPreviewLoading(true);
    try {
      const previewResponse = await postApiQueryFile(
        "postPedidoCartasEtermosGenerateCartaDiluicaoPreview",
        "",
        {
          crm: item.crm,
          idGrupo: item.idGrupo,
          clientePrincipal: clientePrincipal,
          adversoPrincipal: adversoPrincipal,
          grupoCotaContrato: grupoCotaContrato,
          dataRetornoPagamento: dataRetornoPagamento,
          mesesParcelas: mesesParcelas,
        }
      );

      if (previewResponse.ok) {
        const blob = await previewResponse.blob();
        if (blob.type === "application/pdf" || blob.size > 0) {
          const url = URL.createObjectURL(blob);
          setPreviewUrl(url);
          setShowPreview(true);
        } else {
          toast.error("Não foi possível gerar o preview do documento");
        }
      } else {
        toast.error("Erro ao gerar preview do documento");
      }
    } catch (error) {
      console.error("Erro ao gerar preview:", error);
      toast.error("Erro ao gerar preview do documento");
    } finally {
      setPreviewLoading(false);
    }
  };

  const handleClosePreview = () => {
    if (previewUrl) {
      URL.revokeObjectURL(previewUrl);
      setPreviewUrl(null);
    }
    setShowPreview(false);
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal modal-xxl"
      centered
    >
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Editar Carta Diluição - {item?.nrContrato}</h5>
      </CModalHeader>
      {loading && (
        <CModalBody style={{ minHeight: "470px" }}>
          <CardLoading Title="Carregando" Msg="Aguarde..." />
        </CModalBody>
      )}
      {!loading && (
        <CModalBody>
          <div className="row mt-4">
            <div className="col-md-6">
              <label className="pt-1">Cliente Principal:</label>
              <CInput
                className="mr-2 ml-2"
                value={clientePrincipal}
                onChange={(e) => setClientePrincipal(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-6">
              <label className="pt-1">Adverso Principal:</label>
              <CInput
                className="mr-2 ml-2"
                value={adversoPrincipal}
                onChange={(e) => setAdversoPrincipal(e.currentTarget.value)}
              />
            </div>
          </div>

          <div className="row mt-3">
            <div className="col-md-4">
              <label className="pt-1">Grupo/Cota/Contrato:</label>
              <CInput
                className="mr-2 ml-2"
                value={grupoCotaContrato}
                onChange={(e) => setGrupoCotaContrato(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-4">
              <label className="pt-1">Data de Retorno Pagamento:</label>
              <CInput
                className="mr-2 ml-2"
                value={dataRetornoPagamento}
                onChange={(e) => setDataRetornoPagamento(e.currentTarget.value)}
              />
            </div>
            <div className="col-md-4">
              <label className="pt-1">MesesParcelas:</label>
              <CInput
                className="mr-2 ml-2"
                value={mesesParcelas}
                onChange={(e) => setMesesParcelas(e.currentTarget.value)}
              />
            </div>
          </div>
        </CModalBody>
      )}
      <CModalFooter>
        <CButton
          color="warning"
          className="mr-2"
          onClick={handlePreview}
          disabled={previewLoading}
        >
          {previewLoading ? "Gerando Preview..." : "Preview"}
        </CButton>
        <CButton
          color="success"
          className="mr-2"
          onClick={handleSave}
          disabled={loading}
        >
          Salvar Alterações
        </CButton>
        <CButton color="secondary" className="mr-2" onClick={handleClose}>
          Cancelar
        </CButton>
      </CModalFooter>

      {showPreview && (
        <CModal
          show={showPreview}
          onClose={handleClosePreview}
          closeOnBackdrop={false}
          className="preview-modal"
          centered
        >
          <CModalHeader closeButton>
            <h5>Preview da Carta Diluição</h5>
          </CModalHeader>
          <CModalBody style={{ padding: "0", height: "80vh" }}>
            {previewUrl && (
              <iframe
                src={previewUrl}
                style={{
                  width: "100%",
                  height: "100%",
                  border: "none",
                }}
                title="Preview do Carta Diluição"
              />
            )}
          </CModalBody>
          <CModalFooter>
            <CButton color="secondary" onClick={handleClosePreview}>
              Fechar Preview
            </CButton>
          </CModalFooter>
        </CModal>
      )}
    </CModal>
  );
};

export default EditCartaDiluicaoModal;
