import React, { useState, useEffect } from "react";
import {
  CCard,
  CCardBody,
  CCol,
  CDataTable,
  CRow,
  CButton,
  CBadge,
  CInputCheckbox,
  CLabel,
} from "@coreui/react";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import { GET_DATA, PUT_DATA } from "src/api";
import DetalhesAcordoModal from "./DetalhesAcordoModal";
import AbrirNegociacaoModal from "./AbrirNegociacaoModal";
import CalculosNegociacaoModal from "./CalculosNegociacaoModal";
import DetalhesBoletoModal from "./DetalhesBoletoModal";
import VisualizarBoleto from "./VisualizarBoletoModal";
import NegociarModal from "./NegociarModal";
import TableSelectItens from "src/reusable/TableSelectItens";
import { useAuth } from "src/auth/AuthContext";
import { getAcordos, getAcordosManuais } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import Select from "react-select";
const VisualizarAcordos = (props) => {
  const { checkPermission, inforPermissions } = useAuth();
  // const { data } = useMyContext();
  const permissao = {
    modulo: "Visualizar Acordos",
    submodulo: null,
  };
  const permissaoNegociacao = {
    modulo: "Negociação",
    submodulo: "Simular",
  };

  const permissaoNegociacaoDetalhesCalculo = {
    modulo: "Negociação",
    submodulo: "Simular - Detalhes do Cálculo",
  };

  const permissaoNegociacaoBoleto = {
    modulo: "Negociação",
    submodulo: "Boleto",
  };

  const permissaoNegociacaoBoletoDetalhes = {
    modulo: "Negociação",
    submodulo: "Boleto - Detalhes do Boleto",
  };

  const finanaciadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const { id } = props.match.params;

  const [acordos, setAcordos] = useState(null);
  const [acordosManuais, setAcordosManuais] = useState(null);
  const [acordosData, setAcordosData] = useState(null);
  const [parcelasTable, setParcelasData] = useState(null);

  const [selectedAcordo, setSelectedAcordo] = useState(null);
  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState("");

  const [showModalDetalhesAcordo, setShowModalDetalhesAcordo] = useState(false);
  const [showModalDetalhesBoleto, setShowModalDetalhesBoleto] = useState(false);
  const [showModalVisualizarBoleto, setShowModalVisualizarBoleto] =
    useState(false);
  const [showModalNegociar, setShowModalNegociar] = useState(false);
  const [showModalNegociacao, setShowModalNegociacao] = useState(false);
  const [showModalCalculoNegociacao, setShowModalCalculoNegociacao] =
    useState(false);

  const [negociarDados, setNegociarDados] = useState(null);
  const [detalhesBoleto, setDetalhesBoleto] = useState(null);
  const [parcelaBoleto, setParcelaBoleto] = useState(null);
  const [detalhesAcordo, setDetalhesAcordo] = useState(null);
  const [calculoNegociacao, setCalculoNegociacao] = useState(null);
  const [visualizarBoleto, setVisualizarBoleto] = useState(null);

  const [loadingDeal, setLoadingDeal] = useState(false);
  const [loadingDealInstallments, setLoadingDealInstallments] = useState(false);

  const [downBol, setDownBol] = useState(false);

  const acordosColumns = [
    {
      key: "nr_Acordo",
      formatterByObject: (item) => renderCellNrAcordo(item),
      cellStyleCondicional: (item) => {
        const classNameV = rowClassName(item);
        if (classNameV === "selected-row") {
          return { backgroundColor: "lightgray", borderRadius: "5px" };
        }
        return {};
      },
    },
    {
      key: "status",
      formatterByObject: (item) => {
        console.log(item);
        if (item.acordo_manual) {
          return renderCheckBoxUpdateStatusAcordoManual(item);
        }
        return renderBadge(item.status);
      },
    },
    // { key: "origem" },
    { key: "qtde_Parcela", label: "Qtd. Parecelas" },
    {
      key: "vl_Total_Original",
      label: "Valor Original",
      formatter: (item) => formatCurrency(item, false),
    },
    {
      key: "vl_Acordo",
      label: "Valor Acordo",
      formatter: (item) => formatCurrency(item, false),
    },

    {
      key: "vl_Saldo_Acordo",
      label: "Valor Saldo",
      formatter: (item) => formatCurrency(item, false),
    },
    { key: "situacao", label: "Situação" },
    {
      key: "dt_Liberacao",
      label: "Data Liberação",
      formatter: (item) => formatDate(item),
    },
    // { key: "detalhes" },
    { key: "nr_Acordo_Banco", label: "Nr. Acordo Banco", defaultValue: "---" },
    {
      key: "negociacao",
      label: "Negociação",
      formatterByObject: (item) => renderCellNegociacao(item),
    },
    {
      key: "parcelas",
      label: "Parcelas",
      formatterByObject: (item) => renderCellParcelas(item),
    },
  ];

  const parcelasColumns = [
    { key: "nr_Parcela", label: "Parcela" },
    { key: "nr_Plano", label: "Plano" },
    {
      key: "dt_Vencimento",
      label: "Dt. Vencimento",
      formatter: (item) => formatDate(item),
      defaultValue: "---",
    },
    {
      key: "vl_Saldo",
      label: "Valor Saldo",
      formatter: (item) => formatCurrency(item, false),
    },
    {
      key: "vl_Atualizado",
      label: "Valor Atualizado",
      formatter: (item) => formatCurrency(item, false),
    },
    {
      key: "vl_Pago",
      label: "Valor Pago",
      formatter: (item) => formatCurrency(item, false),
    },
    {
      key: "dt_Pagamento",
      label: "Dt. Pagamento.",
      formatterByObject: (item) => renderCellDTPagamento(item),
      defaultValue: "---",
    },
    { key: "atraso", label: "Atraso", defaultValue: "0" },
    {
      key: "dt_Atualizacao",
      label: "Dt. Atualização",
      formatterByObject: (item) => renderCellDTAtualizacao(item),
    },
    {
      key: "dt_Venc_Boleto",
      label: "Dt. Vencimento Boleto",
      formatterByObject: (item) => renderCellDTVencBoleto(item),
      defaultValue: "---",
    },
    {
      key: "linha_Digitavel",
      label: "Linha Digitável",
      defaultValue: "---",
    },
    {
      key: "nr_Boleto",
      label: "Nosso Número",
      defaultValue: "---",
    },
    {
      key: "url_Boleto",
      label: "URL Boleto",
      formatterByObject: (item) => renderCellVisualBoleto(item),
    },
    {
      key: "vl_Honor",
      label: "Valor Honorário",
      formatter: (item) => formatCurrency(item, false),
      defaultValue: "---",
    },
  ];

  async function getParcelas(id_Acordo) {
    const data = {
      IdAcordo: id_Acordo,
      numeroContrato: finanaciadoData.numero_Contrato,
      ...(finanaciadoData.idLinkedGroup &&
        finanaciadoData.idGrupo && {
          groupId: finanaciadoData.idGrupo,
          linkedGroupId: finanaciadoData.idLinkedGroup,
        }),
    };
    const parcelas = await GET_DATA("Datacob/Acordos/Parcelas", data);
    return parcelas;
  }

  async function getParcelasManuais(id_Acordo) {
    const data = { idAcordo: id_Acordo };
    const parcelas = await GET_DATA(
      "AcordoManual/GetParcelasAcordosManuais",
      data
    );
    return parcelas;
  }

  // async function getDetalhesAcordo(id_Negociacao) {
  //   const data = { IdNegociacao: id_Negociacao };
  //   const detalhesAcordo = await GET_DATA("Datacob/Acordos/Parcelas", data);

  // }

  const handleClick = async (item) => {
    let parcelas = item?.acordo_manual
      ? await getParcelasManuais(item?.id_Acordo)
      : await getParcelas(item?.id_Acordo);
    setParcelasData(parcelas);
    setSelectedAcordo(item.id_Acordo);
    setSelectedRow(item.nr_Acordo);
  };

  const parcelasDados = async (item) => {
    setLoadingDealInstallments(true);

    let parcelas = item?.acordo_manual
      ? await getParcelasManuais(item?.id_Acordo)
      : await getParcelas(item?.id_Acordo);

    setParcelasData(parcelas);
    setSelectedRow(item.nr_Acordo);
    setSelectedAcordo(item.id_Acordo);
    setLoadingDealInstallments(false);
  };

  const optionsSelectStatusAcordoManual = [
    { label: "Descumprido", value: "Descumprido" },
    { label: "Aberto", value: "Aberto" },
  ];
  const renderCheckBoxUpdateStatusAcordoManual = (item) => {
    return (
      <>
        <Select
          placeholder="Selecione"
          options={optionsSelectStatusAcordoManual}
          value={optionsSelectStatusAcordoManual.find(
            (opt) => opt.value === item.status
          )} // aqui está a correção
          onChange={(x) => {
            handleUpdateStatusAcordoManual(item, x.value === "Descumprido");
            console.log(item);
          }}
          menuPortalTarget={document.body} // renderiza o menu fora do fluxo normal do DOM
          styles={{
            menuPortal: (base) => ({ ...base, zIndex: 9999 }), // garante que o menu esteja no topo
          }}
        />
      </>
    );
  };
  const handleUpdateStatusAcordoManual = async (item, status) => {
    const data = {
      idAcordo: item.id_Acordo,
      status: status ? "Descumprido" : "Aberto",
    };
    const response = await PUT_DATA(
      "AcordoManual/UpdateStatusAcordoManual",
      data
    );

    if (response) {
      toast.success("Status atualizado com sucesso");
      fecthData();
    } else {
      toast.error("Erro ao atualizar status");
    }
  };
  const renderBadge = (status) => {
    switch (status) {
      case "P":
        return <CBadge color="success">Pago</CBadge>;
      case "A":
        return <CBadge color="info">Aberto</CBadge>;
      case "C":
        return <CBadge color="danger">Cancelado</CBadge>;
      case "Descumprido":
        return <CBadge color="danger">Descumprido</CBadge>;
      default:
        break;
    }
  };

  const renderCellNegociacao = (item) => {
    if (item.acordo_manual) {
      return <div>---</div>;
    }
    return (
      <CButton
        className="button-link py-0 px-0"
        onClick={() => handleNegociacaoBoleto(item)}
        title={inforPermissions(permissaoNegociacaoDetalhesCalculo).view}
        disabled={
          !checkPermission(
            permissaoNegociacaoDetalhesCalculo.modulo,
            "View",
            permissaoNegociacaoDetalhesCalculo.submodulo
          )
        }
      >
        Negociação
      </CButton>
    );
  };

  const renderCellParcelas = (item) => {
    return (
      <CButton
        className="button-link py-0 px-0"
        onClick={() => parcelasDados(item)}
        title={inforPermissions(permissaoNegociacaoDetalhesCalculo).view}
        disabled={
          !checkPermission(
            permissaoNegociacaoDetalhesCalculo.modulo,
            "View",
            permissaoNegociacaoDetalhesCalculo.submodulo
          )
        }
      >
        Mostrar Parcelas
      </CButton>
    );
  };

  const renderCellNrAcordo = (item) => {
    return (
      <div className={rowClassName(item)}>
        <CButton onClick={() => handleClick(item)} className="flat py-0 px-0">
          <strong>{item.nr_Acordo}</strong>
        </CButton>
      </div>
    );
  };

  const renderCellDTVencBoleto = (item) => {
    return item.dt_Venc_Boleto ? (
      <CButton
        className="button-link py-0 px-0"
        onClick={() => handleDetalhesBoleto(item)}
        title={inforPermissions(permissaoNegociacaoBoletoDetalhes).view}
        disabled={
          !checkPermission(
            permissaoNegociacaoBoletoDetalhes.modulo,
            "View",
            permissaoNegociacaoBoletoDetalhes.submodulo
          )
        }
      >
        {formatDate(item.dt_Venc_Boleto)}
      </CButton>
    ) : (
      <>---</>
    );
  };
  const renderCellDTAtualizacao = (item) => {
    return item.dt_Venc_Boleto ? formatDate(item.dt_Venc_Boleto) : <>---</>;
  };

  const renderCellDTPagamento = (item) => {
    return item.dt_Pagamento ? formatDate(item.dt_Pagamento) : <>---</>;
  };

  const renderCellVisualBoleto = (item) => {
    return item.id_Boleto ? (
      <div className="d-flex">
        <CButton
          className="button-link nowrap-cell"
          onClick={() => handleVisualizarBoleto(item)}
          title={inforPermissions(permissaoNegociacaoBoleto).view}
          disabled={
            !checkPermission(
              permissaoNegociacaoBoleto.modulo,
              "View",
              permissaoNegociacaoBoleto.submodulo
            )
          }
        >
          {" "}
          Visualizar Boleto
        </CButton>
        <CButton
          disabled={downBol}
          color="info"
          className={"py-0 px-2"}
          onClick={() => handleBaixarBoleto(item)}
        >
          <i className="cil-arrow-thick-to-bottom"></i>
        </CButton>
      </div>
    ) : (
      <>---</>
    );
  };

  const handleDetalhesAcordo = (item) => {
    setDetalhesAcordo([item]);
    setShowModalDetalhesAcordo(true);
  };

  const handleDetalhesBoleto = async (item) => {
    setParcelaBoleto(item.nr_Parcela);
    const data = {
      IdParcelaAcordo: item.id_Parcela_Acordo,
      numeroContrato: finanaciadoData.numero_Contrato,
      ...(finanaciadoData?.idLinkedGroup &&
        finanaciadoData?.idGrupo && {
          groupId: finanaciadoData?.idGrupo,
          linkedGroupId: finanaciadoData?.idLinkedGroup,
        }),
    };
    const detBoletos = await GET_DATA("Datacob/Acordos/Parcelas/Boleto", data);
    setDetalhesBoleto(detBoletos[0]);
    setShowModalDetalhesBoleto(true);
  };

  const handleNegociacaoBoleto = async (item) => {
    const data = { IdNegociacao: item.id_Negociacao };
    const calculos = await GET_DATA("Datacob/Negociacoes/Calculo", data);
    setCalculoNegociacao(calculos);
    setShowModalCalculoNegociacao(true);
  };

  const handleAbrirNegociacao = () => {
    setShowModalNegociacao(true);
  };

  const handleNegociarModal = () => {
    if (parcelasTable && parcelasTable.length > 0) {
      const filteredData = parcelasTable.filter(
        (item) => item.dt_Pagamento === null
      );
      setNegociarDados(filteredData);
      setShowModalNegociar(true);
    }
  };

  const handleVisualizarBoleto = async (item) => {
    const data = { IdBoleto: item.id_Boleto };
    const boleto = await GET_DATA("Datacob/DownloadBoleto", data);

    // Defina um título para a nova janela
    const windowTitle = "Visualização de PDF";

    // Crie uma nova janela com título
    const newWindow = window.open(
      "",
      "_blank",
      `width=800,height=600,toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,title=${windowTitle}`
    );

    // Escreva o conteúdo do PDF na nova janela
    newWindow.document.open();
    newWindow.document.write(
      '<embed width="100%" height="100%" src="data:application/pdf;base64,' +
        boleto +
        '" type="application/pdf"/>'
    );
    newWindow.document.close();
  };

  const handleBaixarBoleto = async (item) => {
    setDownBol(true);
    try {
      const data = { IdBoleto: item.id_Boleto };
      const boleto = await GET_DATA("Datacob/DownloadBoleto", data);
      const linkSource = `data:application/pdf;base64,${boleto}`;
      const downloadLink = document.createElement("a");
      const time = new Date().toLocaleTimeString().replaceAll(":", "");
      const date = new Date().toLocaleDateString().replaceAll("/", "");
      const fileName = `${item?.nr_Boleto ?? 0}-${date}${time}.pdf`;
      downloadLink.href = linkSource;
      downloadLink.download = fileName;
      downloadLink.click();
    } catch (err) {
      toast.danger("Erro ao baixar boleto");
    }
    setDownBol(false);
  };

  const handleClose = (modal = "") => {
    switch (modal) {
      case "AbrirNegociacao":
        setShowModalNegociacao(false);
        break;

      case "DetalhesBoleto":
        setShowModalDetalhesBoleto(false);
        setDetalhesBoleto(null);
        break;

      default:
        setDetalhesAcordo(null);
        setCalculoNegociacao(null);
        setVisualizarBoleto(null);
        setNegociarDados(null);

        setShowModalNegociar(false);
        setShowModalVisualizarBoleto(false);
        setShowModalCalculoNegociacao(false);
        setShowModalDetalhesAcordo(false);
        break;
    }
  };

  const rowClassName = (item) => {
    return item.nr_Acordo === selectedRow ? "selected-row" : "";
  };

  const handleUpdateModal = (sucesso) => {
    //Se precisar atualizar a tabela quando cancelar um boleto, faz aqui
    //Mas como essa tabela não mostra status do boleto, não precisa
  };

  useEffect(() => {
    if (acordos) {
      const filteredData = acordos.filter((item) => {
        const matchesStatus =
          !selectedStatus ||
          item.status === selectedStatus ||
          (item.acordo_manual && selectedStatus === "Manual");
        return matchesStatus;
      });
      setAcordosData(filteredData);
    }
  }, [selectedStatus]);

  const fecthData = async () => {
    setLoadingDeal(true);
    const acordos = await getAcordos(
      finanaciadoData?.id_Agrupamento,
      finanaciadoData?.numero_Contrato
    );

    const acordosManuais = await getAcordosManuais(
      finanaciadoData?.id_Contrato,
      finanaciadoData?.numero_Contrato
    );
    setAcordosManuais(acordosManuais);

    if (acordosManuais.length > 0) {
      setAcordosData([
        ...acordos,
        ...converterAcordoManualParaAcordo(acordosManuais),
      ]);
      setAcordos([
        ...acordos,
        ...converterAcordoManualParaAcordo(acordosManuais),
      ]);
    } else {
      setAcordosData(acordos);
      setAcordos(acordos);
    }

    setLoadingDeal(false);
  };

  const converterAcordoManualParaAcordo = (listaAcordosManuais) => {
    let newlist = listaAcordosManuais.map((item) => {
      return {
        id_Acordo: item.id,
        id_Negociacao: "--",
        nr_Acordo: item.nrAcordo,
        status: item.status,
        qtde_Parcela: item.totalParcelas,
        vl_Total_Original: item.valorParcela * item.totalParcelas,
        vl_Acordo: item.valorParcela * item.totalParcelas,
        vl_Saldo_Acordo: item.valorParcela * item.totalParcelas,
        situacao: "",
        dt_Acordo: item.dataNegociacao,
        dt_Liberacao: item.dataNegociacao,
        nr_Acordo_Banco: null,
        perc_Desconto: "",
        vl_Base_Acordo: null,
        vl_Base_Multa: null,
        vl_Entrada: item.valorEntrada,
        vl_Iof: 0,
        perc_Financ: 0,
        enviado: false,
        dt_Envio: null,
        tipo_Envio: 99,
        dt_Confirmacao: null,
        dt_Rejeicao: null,
        dt_Aprovacao_Proposta: null,
        email_Bol: null,
        descricao: "",
        dt_Cancel: "",
        descricao_Cancelamento: "",
        crm: item.crm,
        acordo_manual: true,
      };
    });
    return newlist;
  };

  useEffect(() => {
    fecthData();
    const paramsURL = new URLSearchParams(props.location.search);
    const statusSelected = paramsURL.get("statusSelected");

    let listAcordos = acordos;
    if (statusSelected != null && acordos != null) {
      listAcordos = acordos.filter((item) => {
        return item.status == statusSelected;
      });
      setSelectedStatus(statusSelected);
    }
    setAcordosData(listAcordos);
  }, []);

  // useEffect(() => {
  //   parcelasDados();
  // }, [acordosData]);

  return (
    <div>
      <CRow className="mx-3 mb-2">
        <h1>Visualizar Acordos</h1>
      </CRow>
      <CRow className="d-flex mb-2 mx-3">
        <CCol md="6" sm="12" className="px-0 mb-2">
          <CButton
            color={selectedStatus === "" ? "info" : "secondary"}
            className="mr-2"
            onClick={() => setSelectedStatus("")}
          >
            Todos
          </CButton>
          <CButton
            color={selectedStatus === "A" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("A")}
          >
            Aberto
          </CButton>
          <CButton
            color={selectedStatus === "P" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("P")}
          >
            Pago
          </CButton>
          <CButton
            color={selectedStatus === "B" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("B")}
          >
            Boletagem
          </CButton>
          <CButton
            color={selectedStatus === "C" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("C")}
          >
            Cancelado
          </CButton>
          <CButton
            color={selectedStatus === "Manual" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("Manual")}
          >
            Acordo Manual
          </CButton>
        </CCol>
        <CCol></CCol>
        <CCol md="4" sm="12" className="d-flex px-0 mb-2 justify-content-end">
          {/* <CButton
            color="success"
            className="mr-2"
            onClick={() => history.push("/acordo/criar")}
            title={"Criar Acordos"}
            disabled={
              !acordos ||
              !checkPermission(
                permissaoNegociacao.modulo,
                "Create",
                permissaoNegociacao.submodulo
              )
            }
          >
            Criar Acordos
          </CButton> */}
          <CButton
            color="info"
            className="mr-2"
            onClick={handleNegociarModal}
            title={
              !acordos
                ? "Não há Acorodos"
                : inforPermissions(permissaoNegociacao).create
            }
            disabled={
              !acordos ||
              !checkPermission(
                permissaoNegociacao.modulo,
                "Create",
                permissaoNegociacao.submodulo
              )
            }
          >
            Negociar
          </CButton>
          <CButton
            color="info"
            onClick={handleAbrirNegociacao}
            title={
              !acordos
                ? "Não há Acorodos"
                : inforPermissions(permissaoNegociacao).view
            }
            disabled={
              !acordos ||
              !checkPermission(
                permissaoNegociacao.modulo,
                "View",
                permissaoNegociacao.submodulo
              )
            }
          >
            Abrir Negociação
          </CButton>
        </CCol>
      </CRow>
      <div className="container-fluid">
        <CCard>
          <CCardBody>
            {loadingDeal && <CardLoading />}
            {!loadingDeal && acordosData && (
              <TableSelectItens
                data={acordosData}
                columns={acordosColumns}
                onSelectionChange={(_) => {}}
                defaultSelectedKeys={[]}
                selectable={false}
                heightParam="290px"
              />
            )}
          </CCardBody>
        </CCard>
        <CCard>
          <CCardBody>
            {loadingDealInstallments && <CardLoading />}
            {!loadingDealInstallments && parcelasTable && (
              <div style={{ maxHeight: "420px", overflowX: "auto" }}>
                <div style={{ width: "fit-content", overflowY: "auto" }}>
                  <TableSelectItens
                    data={parcelasTable}
                    columns={parcelasColumns}
                    onSelectionChange={(_) => {}}
                    defaultSelectedKeys={[]}
                    selectable={false}
                    heightParam="400px"
                  />
                </div>
              </div>
            )}
          </CCardBody>
        </CCard>
      </div>
      <DetalhesAcordoModal
        isOpen={showModalDetalhesAcordo}
        onClose={handleClose}
        dados={detalhesAcordo}
      />
      <CalculosNegociacaoModal
        isOpen={showModalCalculoNegociacao}
        onClose={handleClose}
        dados={calculoNegociacao}
      />
      <AbrirNegociacaoModal
        isOpen={showModalNegociacao}
        onClose={handleClose}
        dados={parcelasTable}
      />
      <DetalhesBoletoModal
        isOpen={showModalDetalhesBoleto}
        onClose={handleClose}
        dados={detalhesBoleto}
        parcela={parcelaBoleto}
        updateModal={handleUpdateModal}
      />
      <VisualizarBoleto
        isOpen={showModalVisualizarBoleto}
        onClose={handleClose}
        dados={visualizarBoleto}
      />
      <NegociarModal
        isOpen={showModalNegociar}
        onClose={handleClose}
        acordo={selectedAcordo}
        dados={negociarDados?.sort((a, b) => a?.nr_Parcela - b?.nr_Parcela)}
      />
    </div>
  );
};

export default VisualizarAcordos;
