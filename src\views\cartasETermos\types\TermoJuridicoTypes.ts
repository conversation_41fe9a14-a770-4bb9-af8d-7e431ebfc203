export interface ParcelaTermoType {
  id: string;
  infosId: string;
  numero: number;
  vencimento: string; // ISO date string (YYYY-MM-DD)
  valor: number;
}

export interface TermoJuridicoInfosType {
  id: string;
  tipoTermoId: string;
  pedidoId: string;
  jurisdicaoAtual: string;
  nrAtual: string;
  clientePrincipal: string;
  tipoAcao: string;
  adversoPrincipal: string;
  grupoCotaContrato: string;
  nrParcelasVencidas: string;
  valorParcelasVencidas: number;
  multaJuros: number;
  custas: number;
  nrParcelasVincendas: string;
  valorParcelasVincendas: number;
  honorarios: number;
  total: number;
  dataBase: string; // ISO date string (YYYY-MM-DD)
  qtdParcelasAcordadas: number;
  valorAcordado: number;
  descricaoVeiculo: string;
  createdAt: string; // ISO datetime string
  parcelas: ParcelaTermoType[];
  extras: string;
}

export interface TermoJuridicoApiResponse {
  success: boolean;
  data: TermoJuridicoInfosType;
  message?: string;
}

// Type for the internal component state (with Date objects)
export interface ParcelaTermoStateType {
  id: string | null;
  infosId: string | null;
  numero: number;
  dtVencimento: Date;
  valor: number;
}
