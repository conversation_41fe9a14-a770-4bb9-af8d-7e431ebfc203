import React, { useState } from "react";
import { CRow, CCol, CButton, CTooltip } from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import ControleUsuariosEditModal from "./controleUsuariosEditModal";
import ControleUsuariosDetailsModal from "./controleUsuariosDetalisModal";

const ControleUsuariosTable = ({ tickets, getTicketsData }) => {
  const [loading, setLoading] = useState(false);
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showModalEditar, setShowModalEditar] = useState(false);
  const [showModalDetails, setShowModalDetails] = useState(false);

  const handleEditar = (ticket) => {
    setSelectedTicket(ticket);
    setShowModalEditar(true);
  };

  const handleDetais = (ticket) => {
    setSelectedTicket(ticket);
    setShowModalDetails(true);
  };

  const handleCloseModals = () => {
    setShowModalDetails(false);
  };

  const onCloseEdit = async () => {
    setShowModalEditar(false);
    await getTicketsData();
  };

  return (
    <CCol className={"p-4"}>
      <CRow>
        <div
          className="table-responsive"
          style={{ overflow: "auto", maxHeight: "350px" }}
        >
          <table className="table">
            <thead>
              <tr>
                <th>Nome</th>
                <th>Nr. Contrato</th>
                <th>Nr. Boleto</th>
                <th>Nr. Parcela</th>
                <th>Status</th>
                <th>BP</th>
                <th>Carteira</th>
                <th>Usuário</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              {tickets?.length < 1 && (
                <tr>
                  <td colspan="16" className="text-center">
                    Nenhum Registro Encontrado!
                  </td>
                </tr>
              )}
              {tickets?.map((item, index) => {
                return (
                  <tr key={index}>
                    <td>{item.nomeCliente}</td>
                    <td>{item.numeroContrato}</td>
                    <td>{item.numeroBoleto}</td>
                    <td>
                      {item.numeroParcelaEnvolvidas[0]}{" "}
                      {item?.numeroParcelaEnvolvidas?.length > 1 && (
                        <CTooltip
                          content={item.numeroParcelaEnvolvidas
                            .slice(1)
                            .join(", ")}
                        >
                          <span
                            style={{ cursor: "default" }}
                            className="badge badge-info"
                          >
                            +
                          </span>
                        </CTooltip>
                      )}
                    </td>
                    <td>{item.statusBoleto}</td>
                    <td>{item.bp}</td>
                    <td>{item.descricaoCarteira}</td>
                    <td>{item.nomeUsuario}</td>
                    <td className="d-flex">
                      <CTooltip content="Mais Informações">
                        <CButton
                          color="info"
                          onClick={() => handleDetais(item)}
                          className={"mr-2"}
                        >
                          <i className="cil-plus"></i>
                        </CButton>
                      </CTooltip>
                      <CTooltip content="Editar">
                        <CButton
                          color="warning"
                          onClick={() => handleEditar(item)}
                        >
                          <i className="cil-pencil"></i>
                        </CButton>
                      </CTooltip>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </CRow>
      {showModalEditar && (
        <ControleUsuariosEditModal
          isOpen={showModalEditar}
          onClose={onCloseEdit}
          dataEdit={selectedTicket}
        />
      )}
      {showModalDetails && (
        <ControleUsuariosDetailsModal
          isOpen={showModalDetails}
          onClose={handleCloseModals}
          ticket={selectedTicket}
        />
      )}
    </CCol>
  );
};

export default ControleUsuariosTable;
