import { CModalBody } from "@coreui/react";
import React from "react";

const TermoJuridicoProcessosTable = ({ processos, onSelect }) => {
  const handleSelect = (option) => {
    onSelect(option);
  };

  return (
    <>
      <CModalBody>
        <h5>Selecione o processo:</h5>
        <div className="table-responsive">
          <table className="table table-bordered table-hover">
            <thead>
              <tr>
                <th>Nr. Processo</th>
                <th>Fase Atual</th>
                <th>Tipo de Acao</th>
                <th>Jurídico Atual</th>
                <th>Número Atual</th>
                <th>Status</th>
              </tr>
            </thead>
            <tbody>
              {processos?.map((processo, index) => (
                <tr
                  key={`tr${index}`}
                  onClick={() => handleSelect(processo)}
                  className={"cursor-pointer"}
                  style={{
                    cursor: "pointer",
                  }}
                >
                  <td>{processo.numero_atual_processo}</td>
                  <td>{processo.fase_Atual}</td>
                  <td>{processo.tipo_de_Acao}</td>
                  <td>{processo.jurisdicao_Atual}</td>
                  <td>{processo.numero_atual_processo}</td>
                  <td>{processo.status}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </CModalBody>
    </>
  );
};

export default TermoJuridicoProcessosTable;
