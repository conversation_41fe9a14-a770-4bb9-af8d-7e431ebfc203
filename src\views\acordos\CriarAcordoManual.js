import React, { useState, useEffect } from "react";
import {
  <PERSON>ard,
  CCardBody,
  <PERSON>ol,
  <PERSON>ow,
  <PERSON>utton,
  CCardHeader,
  CLabel,
} from "@coreui/react";
import ReactDatePicker from "react-datepicker";
import {
  convertCurrencyToFloat,
  formatCurrency,
  formatDate,
  formatThousands,
  formatarParaMoeda,
  leftPad,
} from "src/reusable/helpers";
import { getApi, postApi, getApiInline } from "src/reusable/functions";
import TableSelectItens from "src/reusable/TableSelectItens";
import { useMyContext } from "src/reusable/DataContext";
import {
  calculaAtrasoParcelasPorDataNegociaco,
  calculaPerncetuaisDesconto,
  calculaValoresParcelas,
} from "../negociacao/utils/CalculosNegociacao";
import { toast } from "react-toastify";
import CardLoading from "src/reusable/CardLoading";
import { useHistory } from "react-router-dom";
import { GET_DATA, POST_DATA } from "src/api";

const CriarAcordos = (props) => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const histoty = useHistory();

  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [textLoading, setTextLoading] = useState("");
  const [validadacaoTela, setValidadacaoTela] = useState("");
  const [textValidacao, setTextValidacao] = useState("");
  const [parametroCalculo, setParametroCalculo] = useState(null);
  const [radioChoice, setRadioChoice] = useState(0);
  const [useEntryValue, setUseEntryValue] = useState(false);
  const [calcPayload, setCalcPayload] = useState({
    installments: [],
    value: 0,
    discount: 0,
    date: new Date(),
    entryValue: 0,
  });
  const [plans, setPlans] = useState([]);
  const [loadingCalc, setLoadingCalc] = useState(false);
  const [loadingConfirm, setLoadingConfirm] = useState(false);

  const [parcelasSelecionadas, setParcelasSelecionadas] = useState([]);
  const [totalParcelasSelecionadas, setTotalParcelasSelecionadas] = useState(1);
  const [valorParcela, setValorParcela] = useState("0");
  const [selectedOcorrencia, setSelectedOcorrencia] = useState(null);
  const [optionsOcorrencia, setOptionsOcorrencia] = useState([]);

  const [reloadScreen, setReloadScreen] = useState(false);

  const { data } = useMyContext();

  const getParametro = () => {
    setLoading(true);
    setTextLoading("Carregando parâmetros de cálculo...");
    const payload = {
      idclientedatacob: data.id_Cliente,
      idfasecontrato: data.id_Fase,
      codorname: data.coddatacob.toString(),
    };
    getApi(payload, "getParametroCalculo")
      .then((par_calc) => {
        if (!par_calc || par_calc.length === 0 || par_calc === undefined) {
          setTextValidacao(
            `Não há parâmetro de cálculo para o grupo ${data?.grupo}.`
          );
          setValidadacaoTela(false);
        }
        setParametroCalculo(par_calc);
      })
      .catch((error) => {
        setTextValidacao(
          `Falha: Não há parâmetro de cálculo para o grupo ${data?.grupo}.`
        );
        setValidadacaoTela(false);
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const columns = [
    {
      key: "numero_Contrato",
      defaultSort: "ascending",
      label: "Contrato",
      filter: true,
    },
    {
      key: "nr_Parcela",
      label: "Parcela",
      cellStyleCondicional: (item) => {
        if (item.atraso && item.atraso > 0) {
          return {
            backgroundColor: "red",
            color: "white",
            textAlign: "center",
          };
        }
        return {
          backgroundColor: "white",
          color: "black",
          textAlign: "center",
        };
      },
      formatter: (value) => String(value).padStart(3, "0"),
    },
    { key: "nr_Plano", label: "Plano" },
    {
      key: "nome_Tipo_Parcela",
      label: "Tp. Parcela",
    },
    {
      key: "dt_Vencimento",
      label: "Vencimento",
      formatter: (value) => formatDate(value),
    },
    {
      key: "vl_Saldo",
      label: "Valor Saldo",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "vl_Saldo_Atualizado",
      label: "Valor Total",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "valorNegociado",
      label: "Valor Negociado",
      formatter: (value) => formatCurrency(value, false),
    },
    { key: "atraso", formatter: (value) => value.toString() },
    //Esses dois campos de desconto são provavelmente calculados aqui no front e carregados na tabela
    {
      key: "desconto",
      label: "Desconto",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "percDesconto",
      label: "% de Desconto",
      formatter: (value) => formatCurrency(value, false),
    },
  ];

  const columnsCalc = [
    {
      key: "qtdeParcelas",
      defaultSort: "ascending",
      label: "Parcelamento",
      formatter: (value) =>
        value === 0 ? "À vista" : String(value + 1).padStart(2, "0"),
    },
    {
      key: "valorEntradaMinimo",
      label: "Ent. Mínima",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "valorParcelaSemDesconto",
      label: "Sem Desc.",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "valorDesconto",
      label: "Desc. Máximo",
      formatterByObject: (value) =>
        formatCurrency(value.valorParcela - value.valorDesconto, false),
    },
    {
      key: "percentualParcelamento",
      label: "",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "valorAcordo",
      label: "% Desc",
      formatterByObject: (value) =>
        formatCurrency(
          Math.abs(
            parseFloat(
              (-(
                ((value.entrada + value.valorParcela * value.qtdeParcelas) *
                  100) /
                  value.valorAcordo -
                100
              )).toFixed(2)
            )
          ),
          false
        ),
    },
    {
      key: "entrada",
      label: "Acordo",
      formatterByObject: (value) =>
        value.qtdeParcelas === 0 ? (
          <span>À vista: {formatCurrency(value.entrada, false)}</span>
        ) : (
          <span>
            Ent. {value.entrada} + {String(value.qtdeParcelas).padStart(2, "0")}
            : {value.valorParcela}{" "}
          </span>
        ),
    },
    {
      key: "",
      label: "",
      formatterByObject: (value) => {
        return (
          <CButton color="success" onClick={() => handleConfirmDeal(value)}>
            Confirmar
          </CButton>
        );
      },
    },
  ];

  const handleConfirmDeal = async (item) => {
    setLoadingCalc(true);
    calcPayload.date.setHours(calcPayload.date.getHours() - 3);

    const response = await postApi(
      {
        idContrato: item.idContrato,
        valorEntrada: item.entrada,
        dataNegociacao: item.dataNegociacao,
        parcelas: item.parcelas,
        qtdeParcelas: item.qtdeParcelas,
        modalidadeNegociacao: item.modalidadeNegociacao,
        dataPagtoEntrada: item.dataNegociacao,
        valorParcela: item.qtdeParcelas === 0 ? 0 : item.valorParcela,
        ...(data.idLinkedGroup &&
          data.idGrupo && {
            groupId: data.idGrupo,
            linkedGroupId: data.idLinkedGroup,
          }),
      },
      "postConfirmarAcordo"
    );
    if (
      response !== null &&
      response !== undefined &&
      response?.data?.idAcordo !== null &&
      response?.success === true
    ) {
      histoty.push("/acordos/visualizar");
    } else {
      const error =
        response?.message.indexOf("Retorno Api.") > -1
          ? "Erro CRM: " +
            response?.message.replace('Retorno Api. ["', "").replace('"]', "")
          : response?.message;
      toast.error(error);
    }
    setLoadingCalc(false);
  };

  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;
  const parcelasAbertas =
    contratosAtivos === null || contratosAtivos === undefined
      ? []
      : contratosAtivos?.flatMap((item) =>
          item.parcelas.filter(
            (pItem) =>
              pItem.status === "A" &&
              !pItem.nr_Acordo /* && pItem.atraso > 0  ||
        (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
          )
        );

  const HandleInstallmentChange = (itens) => {
    setParcelasSelecionadas(itens);
    let totalParcelas = itens.length > 1 ? itens.length : 1;
    setTotalParcelasSelecionadas(totalParcelas);
    let valorParcela = 0;
    if (itens.length > 0) {
      valorParcela = calcularValorParcela(
        itens,
        totalParcelas,
        calcPayload.entryValue
      );
    }
    setValorParcela(formatCurrency(valorParcela, false));

    setCalcPayload({ ...calcPayload, installments: itens });
  };

  const calcularValorParcela = (
    parcelasSelecionadas,
    qtdeParcelas,
    valorEntrada
  ) => {
    let _valorParcela = parcelasSelecionadas.reduce(
      (p, x) => p + x.vl_Saldo_Atualizado,
      0
    );
    if (parcelasSelecionadas.length === 0)
      _valorParcela = parseFloat(valorParcela);
    _valorParcela = _valorParcela - valorEntrada;
    if (_valorParcela < 0) _valorParcela = 0;
    _valorParcela = _valorParcela / qtdeParcelas;
    return _valorParcela;
  };

  const HandleDateChange = (e) => {
    setCalcPayload({ ...calcPayload, date: e });
  };

  const HandleValueChange = (e) => {
    const sanitizedValue = e.target.value.replace(/[^\d]/g, "");
    const formattedValue = formatarParaMoeda(sanitizedValue);
    const formattedValueF = formattedValue.replace(/\./g, "").replace(",", ".");
    if (isNaN(Number(formattedValueF))) return false;
    setCalcPayload({ ...calcPayload, value: parseFloat(formattedValueF) });
  };

  const HandleDiscountChange = (e) => {
    const sanitizedValue = e.target.value.replace(/[^\d]/g, "");
    const formattedValue = formatarParaMoeda(sanitizedValue);
    const formattedValueF = formattedValue.replace(/\./g, "").replace(",", ".");
    const value = parseFloat(formattedValueF);
    if (isNaN(value)) return false;
    if (value > 100) {
      setCalcPayload({ ...calcPayload, discount: 100 });
      return false;
    }
    setCalcPayload({ ...calcPayload, discount: value });
  };

  const HandleEntryValueChange = (e) => {
    const sanitizedValue = e.target.value.replace(/[^\d]/g, "");
    const formattedValue = formatarParaMoeda(sanitizedValue);
    const formattedValueF = parseFloat(
      formattedValue.replace(/\./g, "").replace(",", ".")
    );
    if (isNaN(Number(formattedValueF))) return false;
    setCalcPayload({ ...calcPayload, entryValue: formattedValueF });
    setValorParcela(
      formatCurrency(
        calcularValorParcela(
          parcelasSelecionadas,
          totalParcelasSelecionadas,
          formattedValueF
        ),
        false
      )
    );
  };

  const handleValorParcelaChange = (e) => {
    const sanitizedValue = e.target.value.replace(/[^\d]/g, "");
    const formattedValue = formatarParaMoeda(sanitizedValue);
    const formattedValueF = parseFloat(
      formattedValue.replace(/\./g, "").replace(",", ".")
    );
    if (isNaN(Number(formattedValueF))) return false;

    setValorParcela(formattedValueF);
  };

  const calcDeal = async () => {
    const confirmed = window.confirm(
      "Tem certeza que deseja criar esse acordo?"
    );
    if (!confirmed) return;

    setLoadingCalc(true);
    const parcelas = calcPayload.installments.reduce((p, x) => {
      p.push(x.id_Parcela);
      return p;
    }, []);

    const valorTotal = calcPayload.installments.reduce((p, x) => {
      p += x.valorNegociado;
      return p;
    }, 0);

    calcPayload.date.setHours(calcPayload.date.getHours() - 3);

    const payload = {
      idContrato: data.id_Contrato,
      idFinanciado: data.id_Financiado,
      numeroContrato: data.numero_Contrato,
      valorEntrada: useEntryValue ? calcPayload.entryValue : 0,
      dataNegociacao: calcPayload.date.toISOString(),
      parcelas: parcelas,
      totalParcelas: totalParcelasSelecionadas,
      valorParcela:
        typeof valorParcela === "number"
          ? valorParcela
          : convertCurrencyToFloat(valorParcela),
      crm: data.coddatacob,
    };
    postApi(payload, "postGerarAcordoManual")
      .then((response) => {
        console.log("response", response);
        toast.success("Acorodo gerado com sucesso!");
        handleSaveOccurrence();
        setLoadingCalc(false);
        getParametro();
      })
      .catch((error) => {
        console.log("error", error);
        toast.error("Erro ao gerar acordo!");
        setLoadingCalc(false);
        getParametro();
      });
  };

  async function getTiposOcorrencia() {
    setLoading(true);
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    if (tiposOcorrencia !== null && tiposOcorrencia !== undefined) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.cod_Ocorr_Sistema + " - " + item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];

      setOptionsOcorrencia(options);
      return options;
    }
    setLoading(false);
    return [];
  }

  const telefones =
    JSON.parse(localStorage.getItem("clientData")).telefones ?? [];

  const handleSaveOccurrence = async () => {
    // if (selectedPhone === null || selectedOcorrencia === null) {
    //   toast.warning("Selecione o telefone e a ocorrência!");
    //   return;
    // }
    let telefoneSelecionado =
      telefones.find((x) => x.status === 1) || telefones[0];
    if (telefoneSelecionado === undefined) {
      toast.warning("Telefone não encontrado!");
      return;
    }
    let listaOcorrencias = await getTiposOcorrencia();
    let ocorrenciaSelecionada = listaOcorrencias.find(
      (x) => x.cod_Ocorr_Sistema === "042"
    );

    if (ocorrenciaSelecionada === undefined) {
      toast.warning("Ocorrência 042 não encontrada!");
      return;
    }
    toast.info("Enviando ocorrência...");
    const payload = {
      login: user?.username,
      id_Contrato: data?.id_Contrato,
      id_Ocorrencia_Sistema: ocorrenciaSelecionada?.value,
      observacao: `Acordo gerado para o contrato ${data?.numero_Contrato}.`,
      telefones: [telefoneSelecionado.ddd + telefoneSelecionado.fone],
      complemento: "",
      telefoneParaRetorno: `${telefoneSelecionado.ddd}${telefoneSelecionado.fone}`,
      ...(data?.idLinkedGroup &&
        data?.idGrupo && {
          groupId: data?.idGrupo,
          linkedGroupId: data?.idLinkedGroup,
        }),
    };
    const ocorrencia = await POST_DATA(
      "Datacob/historicoAdicionar",
      payload,
      false,
      true
    );
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
    } else {
      toast.error("Erro ao enviar ocorrência!");
    }
  };

  const validacoeTela = () => {
    setValidadacaoTela(true);
    setTextValidacao("");
    setLoading(true);
    let keep = true;

    getApiInline("grupos_criacao_acordo", "getConfigUnicaByKey")
      .then((config) => {
        if (!config || config === undefined) {
          setTextValidacao(
            `Não há parâmetro de cálculo para o(s) grupo(s) do usuário`
          );
          setValidadacaoTela(false);

          keep = false;
        } else {
          keep = false;
          const grupos = JSON.parse(config);
          for (const item of user?.groups) {
            if (grupos.indexOf(item.id) > -1) {
              keep = true;
              break;
            }
          }
          if (!keep) {
            setTextValidacao(
              `Não há parâmetro de cálculo para o(s) grupo(s) do usuário`
            );
            setValidadacaoTela(false);
          }
        }
      })
      .catch((error) => {
        setTextValidacao(
          `Falha: Não há permissão de criação para o(s) grupo(s) do usuário.`
        );
        setValidadacaoTela(false);
        keep = false;
      })
      .finally(() => {
        setLoading(false);
      });

    if (!keep) return false;
    if (!contratosAtivos || !parcelasAbertas || parcelasAbertas.length === 0) {
      setValidadacaoTela(false);
      setTextValidacao(
        "Não há parcelas abertas e em atraso para este agrupamento."
      );
      return false;
    }
    return true;
  };

  const recalculorNegociado = (
    parcelasRecalcula,
    valorNegociadoAtualizar = null
  ) => {
    const selectedDate = new Date();
    const parcelas = JSON.parse(JSON.stringify(parcelasRecalcula));
    setLoading(true);
    setTextLoading("Calculando...");

    if (parcelas && parcelas.length === 0) {
      return;
    }

    const recalculoParcelasData = calculaAtrasoParcelasPorDataNegociaco(
      parcelas,
      selectedDate
    );
    const recalculoParcelas = calculaValoresParcelas(
      parametroCalculo,
      recalculoParcelasData,
      valorNegociadoAtualizar ?? 0,
      selectedDate
    );
    const recaculoPercentuaisParcela = calculaPerncetuaisDesconto(
      parametroCalculo,
      recalculoParcelas,
      Math.round((valorNegociadoAtualizar ?? 0) * 100) / 100
    );

    setTableData(recaculoPercentuaisParcela);
    setLoading(false);
  };

  useEffect(() => {
    getParametro();
  }, []);

  useEffect(() => {
    if (parametroCalculo) {
      const parcelas = JSON.parse(JSON.stringify(parcelasAbertas));
      const ordenarParcelas = parcelas.sort((a, b) => {
        // Verifica se a.nr_Parcela ou b.nr_Parcela é igual a 0
        if (a.nr_Parcela === 0) return 1; // Move 'a' para o final se a.nr_Parcela é 0
        if (b.nr_Parcela === 0) return -1; // Move 'b' para o final se b.nr_Parcela é 0

        // Caso contrário, ordena normalmente
        return a.nr_Parcela - b.nr_Parcela;
      });
      const ordenarContrato = ordenarParcelas.sort(
        (a, b) => a.numero_Contrato - b.numero_Contrato
      );

      const parcelasSel = ordenarContrato.map((item) => {
        item.parcelaSelecionada = item.atraso > 0 ?? item.nr_Parcela === 0;
        return item;
      });
      recalculorNegociado(parcelasSel);
      validacoeTela();
    }
  }, [parametroCalculo]);

  return (
    <>
      {validadacaoTela && !loading && (
        <div>
          <CRow className="mx-3 mb-2">
            <h1>Criar Acordos Manuais</h1>
          </CRow>
          <CRow className="d-flex mb-2 mx-3"></CRow>
          <CCard>
            <CCardHeader>
              <strong>Selecione as parcelas desejadas:</strong>
            </CCardHeader>
            <CCardBody>
              <CRow className="mt-2">
                <CCol>
                  <TableSelectItens
                    data={tableData}
                    columns={columns}
                    onSelectionChange={HandleInstallmentChange}
                    selectable={true}
                    heightParam="234px"
                  />
                </CCol>
              </CRow>
            </CCardBody>
          </CCard>
          <CCard>
            <CCardHeader>
              <strong>Dados do acordo:</strong>
            </CCardHeader>
            <CCardBody>
              <CRow className="mt-2">
                <CCol xs="2">
                  <CLabel className={"form-label"}>Data do Acordo:</CLabel>
                  <br />
                  <ReactDatePicker
                    selected={calcPayload.date}
                    onChange={HandleDateChange}
                    className="form-control"
                    minDate={new Date()}
                    dateFormat="dd/MM/yyyy"
                    onKeyDown={(e) => e.preventDefault()}
                  />
                </CCol>

                <CCol xs="2">
                  <div className="text-center">
                    <label
                      className={"form-check-label"}
                      htmlFor="flexSwitchCheckDefault"
                    >
                      Informar o valor de entrada
                    </label>
                  </div>
                  <div
                    className={
                      "d-flex justify-content-center form-check form-switch"
                    }
                  >
                    <input
                      className={"form-check-input"}
                      type="checkbox"
                      role="switch"
                      id="flexSwitchCheckDefault"
                      checked={useEntryValue}
                      onChange={(e) => {
                        setUseEntryValue(e.target.checked);
                        setCalcPayload({ ...calcPayload, entryValue: 0 });
                      }}
                    />
                  </div>
                </CCol>
                <CCol xs="2">
                  <CLabel className={"form-label"}>Valor de Entrada:</CLabel>
                  <input
                    style={{
                      width: "100%",
                      paddingLeft: "8px",
                      justifyContent: "start",
                    }}
                    className="form-control"
                    type="text"
                    disabled={!useEntryValue}
                    onChange={HandleEntryValueChange}
                    value={formatCurrency(calcPayload.entryValue, false)}
                  />
                </CCol>
                <CCol xs="2">
                  <CLabel className={"form-label"}>Qtd Parcelas:</CLabel>
                  <input
                    style={{
                      width: "100%",
                      paddingLeft: "8px",
                      justifyContent: "start",
                    }}
                    className="form-control"
                    type="text"
                    onChange={(e) => {
                      setTotalParcelasSelecionadas(parseInt(e.target.value));
                    }}
                    value={totalParcelasSelecionadas}
                  />
                </CCol>
                <CCol xs="2">
                  <CLabel className={"form-label"}>Valor das Parcelas:</CLabel>
                  <input
                    style={{
                      width: "100%",
                      paddingLeft: "8px",
                      justifyContent: "start",
                    }}
                    className="form-control"
                    type="text"
                    onChange={handleValorParcelaChange}
                    value={formatCurrency(valorParcela, false)}
                  />
                </CCol>
                <CCol xs="2" className="pt-4">
                  <CButton
                    onClick={calcDeal}
                    color="primary"
                    disabled={loadingCalc}
                  >
                    {loadingCalc ? "Gerando..." : "Gerar Acordo"}
                  </CButton>
                </CCol>
              </CRow>
            </CCardBody>
          </CCard>

          {/* <CCard>
            <CCardHeader>
              <strong>Cálculo do Acordo:</strong>
            </CCardHeader>
            <CCardBody>
              <CRow className="mt-2">
                <CCol>
                  {!loadingConfirm ? (
                    <TableSelectItens
                      data={plans}
                      columns={columnsCalc}
                      onSelectionChange={(_) => {}}
                      defaultSelectedKeys={[]}
                      selectable={false}
                      heightParam="500px"
                    />
                  ) : (
                    <CardLoading />
                  )}
                </CCol>
              </CRow>
            </CCardBody>
          </CCard> */}
        </div>
      )}
      {!validadacaoTela && !loading && (
        <CRow>
          <CCard style={{ width: "100%" }}>
            <CCardBody>
              <div className="text-center" style={{ width: "100%" }}>
                <label>{textValidacao}</label>
              </div>
            </CCardBody>
          </CCard>
        </CRow>
      )}
      {loading && (
        <CRow>
          <CCard style={{ width: "100%" }}>
            <CCardBody>
              <div className="text-center">
                <CardLoading />
              </div>
            </CCardBody>
          </CCard>
        </CRow>
      )}
    </>
  );
};

export default CriarAcordos;
