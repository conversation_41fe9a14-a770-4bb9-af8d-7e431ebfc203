import React, { useState, useEffect } from "react";
import { CChartBar } from '@coreui/react-chartjs'
import { hexToRgba } from '@coreui/utils'
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

const MainChartContracts = ({style, filter }) => {
  const [chartData, setChartData] = useState({ datasets: [{ backgroundColor: "rgba(77, 189, 116, 0.1)", borderColor: "#4dbd74", borderWidth: 2, label: "", data: [0] }], labels: ["01"] });
  const [loading, setLoading] = useState(false);

  const getDados = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    setLoading(true);
    defaultDatasets(filter).then(({ datasets, labels }) => {
      setChartData({ datasets, labels });
      setLoading(false);
    });
  }, [filter]);

  const Color = (crm) => {
    return { Rodobens: '#4dbd74', GVC: '#20a8d8' }[crm] || '#f86c6b';
  };

  async function agruparDados(data) {
    const groupedMap = new Map();
    const labelsSet = new Set();
    let maxValue = 0;

    data?.forEach(item => {
      const day = new Date(item.dt_Contr).getDate().toString().padStart(2, '0');
      const key = `${item.crm}_${day}`;

      labelsSet.add(day);

      if (!groupedMap.has(key)) {
        groupedMap.set(key, {
          key,
          crm: item.crm,
          day,
          count: 0,
          list: []
        });
      }

      const group = groupedMap.get(key);
      group.count += item.count;
      group.list.push(item);
      if (group.count > maxValue) maxValue = group.count;
    });

    return {
      grouped: Array.from(groupedMap.values()),
      labels: Array.from(labelsSet).sort(),
      maxValue
    };
  }

  function montaDatasetGrafico(grouped, labels) {
    const datasetsMap = new Map();

    grouped.forEach(group => {
      const index = labels.indexOf(group.day);
      if (!datasetsMap.has(group.crm)) {
        datasetsMap.set(group.crm, {
          label: group.crm,
          backgroundColor: hexToRgba(Color(group.crm), 10),
          borderColor: Color(group.crm),
          pointHoverBackgroundColor: Color(group.crm),
          borderWidth: 2,
          data: new Array(labels.length).fill(0)
        });
      }

      const dataset = datasetsMap.get(group.crm);
      dataset.data[index] = group.count;
    });

    return Array.from(datasetsMap.values());
  }

  async function defaultDatasets(filterActive) {
    try {
      const payload = {
        crm: filterActive?.crm ?? "",
        groupId: filterActive?.groupId ?? "",
        startDate: filterActive?.inicio ?? new Date().toISOString().substring(0, 10),
        endDate: filterActive?.fim ?? new Date().toISOString().substring(0, 10)
      };

      const data = await getDados(payload, "getDashbordContract");
      if (!data) return [];

      const { grouped, labels, maxValue } = await agruparDados(data);
      const datasets = montaDatasetGrafico(grouped, labels);

      return { datasets, labels, maxValue };
    } catch (err) {
      console.error(err);
      return { datasets: [], labels: [], maxValue: 0 };
    }
  }

  const defaultOptions = (() => {
    return {
      maintainAspectRatio: false,
      legend: {
        display: true
      },
      scales: {
        xAxes: [{
          display: true
        }],
        yAxes: [{
          display: true
        }]
      }
    }
  })();

  if (loading) return <div>Carregando gráfico...</div>;
  if (!chartData.datasets.length || !chartData.labels.length) return <div>Nenhum dado para exibir.</div>;

  return (
    <CChartBar style={style}
      datasets={chartData.datasets ?? []}
      options={defaultOptions}
      labels={chartData.labels ?? []}
    />
  );
};

export default MainChartContracts;
