import React, { useEffect, useRef, useState } from "react";
import {
  <PERSON>utton,
  CCard,
  CCardBody,
  CRow,
  CCol,
  CCardFooter,
  CLabel,
  CInput,
  CTabs,
  CNav,
  CTabPane,
  CTabContent,
  CNavItem,
  CNavLink,
} from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import CardLoading from "src/reusable/CardLoading";
import ReactDatePicker from "react-datepicker";
import Select from "react-select";
import { postApi } from "src/reusable/functions";
import NewconTableInstallment from "./components/NewconInstallmentTable";
import LoadingComponent from "src/reusable/Loading";
import TableInstallment from "../Parcial/TableInstallment";
import SendEmailModal from "./SendEmailModal";

const QuitacaoParcelada = ({
  ocorrencia,
  onSave,
  onClose,
  cleanCalculoPost,
  valorMinRegua,
  valorMaxRegua,
}) => {
  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;
  const [tableDataNewcon, setTableDataNewcon] = useState([]);
  const [primeiraParcela, setPrimeirParcela] = useState(new Date().toJSON());
  const [demaisParcelas, setDemaisParcelas] = useState(new Date().toJSON());
  const [optionsEmail, setOptionsEmail] = useState([]);
  const [email, setEmail] = useState(null);
  const [data, setData] = useState([]);
  const [vlDesejado, setVlDesejado] = useState(valorMaxRegua);
  const [vlEntrada, setVlEntrada] = useState(0);
  const [qtdParcela, setQtdParcela] = useState(1);
  const [isLoading, setLoading] = useState(false);
  const [selectedOcorrencia, setSelectedOcorrencia] = useState(null);
  const [optionsOcorrencia, setOptionsOcorrencia] = useState([]);
  const [selectedPhone, setSelectedPhone] = useState(null);
  const [optionsPhone, setOptionsPhone] = useState([]);
  const [vlHo, setVlHo] = useState(null);
  const [percHO, setPercHO] = useState(null);
  const [checkedAllNewcon, setCheckedAllNewcon] = useState(false);
  const [selectedContratoNewcon, setSelectedContratoNewcon] = useState(null);
  const [isLoadingDatacob, setLoadingDatacob] = useState(false);
  const [isLoadingNewcon, setLoadingNewcon] = useState(false);
  const textData = useRef(null);
  const [selectedContract, setSelectedContract] = useState("");
  const [tableData, setTableData] = useState([]);
  const [showSendEmailModal, setShowSendEmailModal] = useState(false);
  const [numerosParcelasSelecionadas, setNumerosParcelasSelecionadas] =
    useState("");
  const [parcelamento, setParcelamento] = useState(1);
  const [negociador, setNegociador] = useState("");
  const [ativo, setAtivo] = useState("");
  const [statusProjuris, setStatusProjuris] = useState("");

  async function getCalculo() {
    const pay = [];
    const honor = vlDesejado * (percHO / 100);
    setVlHo(honor);
    let qtdeParcelasInt = parseInt(qtdParcela);
    if (isNaN(qtdeParcelasInt)) {
      qtdeParcelasInt = 1;
      setQtdParcela(qtdeParcelasInt);
    }
    if (qtdeParcelasInt <= 0 || qtdeParcelasInt === null) {
      toast.error("O campo Qtd. Parcelas (acordo) deve ser maior que 0");
    }

    const parcelasDcSelecionados = tableData.filter(
      (x) => x.parcelaSelecionada
    );
    const parcelasNcSelecionados = tableDataNewcon.filter(
      (x) => x.parcelaSelecionada
    );
    const contratosSelecionados = [
      ...new Set([
        ...parcelasDcSelecionados.flatMap((x) =>
          x.nrContrato.replaceAll(" ", "")
        ),
        ...parcelasNcSelecionados.flatMap((x) =>
          x.nrContrato.replaceAll(" ", "")
        ),
      ]),
    ];

    for (const contrato of contratosSelecionados) {
      const parcDc = parcelasDcSelecionados.filter(
        (x) => x.nrContrato.replaceAll(" ", "") === contrato
      );
      const parcNc = parcelasNcSelecionados.filter(
        (x) => x.nrContrato.replaceAll(" ", "") === contrato
      );

      const parcDcVencidas = parcDc.filter((x) => x.atraso > 0);
      const parcNcVencidas = parcNc.filter((x) => x.vencida);

      const parcDcVincendo = parcDc.filter((x) => x.atraso === 0);
      const parcNcVincendo = parcNc.filter((x) => !x.vencida);

      const vlCustas =
        parcDcVencidas.reduce(
          (a, b) =>
            a +
            b.vlDespesasNegociado +
            b.vlNotificacaoNegociado +
            b.vlTarifaNegociado,
          0
        ) + parcNcVencidas.reduce((a, b) => a + b.vlTarifa, 0);

      const vlSaldoVencido =
        parcDcVencidas.reduce((a, b) => a + b.vlAtualizado, 0) +
        parcNcVencidas.reduce((a, b) => a + b.vlSaldo, 0);

      const vlSaldoVincendo =
        parcDcVincendo.reduce((a, b) => a + b.vlAtualizado, 0) +
        parcNcVincendo.reduce((a, b) => a + b.vlSaldo, 0);

      // const valorHonorario =
      //   vlSaldoVincendo > 0
      //     ? vlHo > 0
      //       ? vlHo
      //       : vlSaldoVincendo * (percHo / 100)
      //     : 0;

      // const percentualHonorario =
      //   vlSaldoVincendo > 0
      //     ? percHo > 0
      //       ? percHo
      //       : (vlHo / vlSaldoVincendo) * 100
      //     : 0;

      const vlTotal = vlSaldoVincendo + vlSaldoVencido + vlHo;
      const vlParcDiff = parcDcVincendo
        .filter((x) => x.nome_Tipo_Parcela === "DIF_PARCELAS")
        .reduce((a, b) => a + b.vlAtualizado, 0);
      pay.push({
        contrato: parcDc[0]?.nrContrato ?? parcNc[0]?.nrContrato,
        custas: vlCustas,
        idContrato: null,
        parcDiff: vlParcDiff,
        parcVencidasDc: parcDcVencidas.flatMap((x) => x.nrParcela).join(","),
        parcVencidasNc: parcNcVencidas.flatMap((x) => x.noParcela).join(","),
        parcVincendoDc: parcDcVincendo.flatMap((x) => x.nrParcela).join(","),
        parcVincendoNc: parcNcVincendo.flatMap((x) => x.noParcela).join(","),
        vlParcVincendoDc: parcDcVincendo[0]?.vlAtualizado ?? 0,
        vlParcVincendoNc: parcNcVincendo[0]?.vlSaldo ?? 0,
        vlHO: vlHo ?? 0,
        vlVencido: vlSaldoVencido,
        vlVincendo: vlSaldoVincendo,
        // percHO: percentualHonorario,
        vlTotal: vlTotal,
      });
    }
    const totalNeg = pay.reduce((a, b) => a + b.vlTotal, 0);
    // setValid(totalNeg >= valorMinimo);
    setData(pay);
  }

  async function postCalculo(payload) {
    const result = await POST_DATA("Simulacoes/QuitacaoParcelada", payload);
    return;
  }

  const buildText = (label, value = 0) => {
    if (
      label === "Saldo Vencido" ||
      label === "Parcela/Vincendo" ||
      label === "Valor H.O." ||
      label === "Custas" ||
      label === "Saldo à Vencer" ||
      label === "Saldo Total" ||
      label === "Valor H.O. à Vencer"
    ) {
      return `${label}: R$ ${formatThousands(value)}`;
    }
    if (label === "H.O. à Vencer") {
      return `${label}: ${value.toFixed(2)}%`;
    } else {
      return `${label}: ${value}`;
    }
  };

  const fieldMappings = [
    // { label: "Contrato", field: "contrato" },
    // { label: "Parcelas Vencidas DataCob", field: "parcVencidasDc" },
    // { label: "Parcelas Vencidas NewCon", field: "parcVencidasNc" },
    // { label: "Dif. Parcela", field: "parcDiff" },

    // { label: "Valor desejado", field: "vlDesejado" },
    // { label: "Qtd Parcelas (acordo)", field: "qtdParcelas" },
    // { label: "Valor Entrada", field: "vlEntrada" },
    // { label: "Valor das Parcelas (acordo)", field: "valorParcelas" },
    // { label: "Valor H.O.", field: "vlHO" },
    // { label: "Parcelas Selecionadas", field: "parcelasselecionadas" },

    // { label: "Negociador", field: "negociador" },
    // { label: "Ativo", field: "ativo" },
    // { label: "Status Projuris", field: "statusProjuris" },
    // { label: "E-mail Cliente", field: "emailCliente" },
    // { label: "Data da 1ª Parcela", field: "dataPrimeiraParcela" },
    // { label: "Data das Demais Parcela", field: "dataDemaisParcela" },
    // { label: "Total Negociação", field: "totalNegociacao" },
    // { label: "Parcelamento", field: "parcelamento" },
    // { label: "Valor Parcela", field: "valorParcela" },
    // { label: "Entrada", field: "vlEntrada" },
    // { label: "Demais Parcelas", field: "demaisParcelas" },

    // { label: "Detalhes Contratos", field: "detalhesContratos" },

    { label: "Contrato", field: "contrato" },
    { label: "Parcelas Vencidas DataCob", field: "parcVencidasDc" },
    { label: "Parcelas Vencidas NewCon", field: "parcVencidasNc" },
    { label: "Dif. Parcela", field: "parcDiff" },
    {
      label: "Custas",
      field: "custas",
    },
    { label: "Saldo Vencido", field: "vlVencido" },
    { label: "Parcelas à Vencer DataCob", field: "parcVincendoDc" },
    { label: "Parcelas à Vencer NewCon", field: "parcVincendoNc" },
    { label: "Qtde à Vencer", field: "qtdVincendo" },
    { label: "Parcela/Vincendo", field: "vlParcVincendo" },
    { label: "Valor H.O. à Vencer", field: "vlHO" },
    { label: "H.O. à Vencer", field: "percHO" },
    { label: "Saldo à Vencer", field: "vlVincendo" },
    { label: "Saldo Total", field: "vlTotal" },
  ];
  const SELECTED_DATE = new Date();

  const COLUMNS = [
    {
      label: "",
    },
    {
      key: "numero_Contrato",
      defaultSort: "ascending",
      label: "Contrato",
      filter: true,
    },
    {
      key: "nr_Parcela",
      label: "Parcela",
      cellStyleCondicional: (item) => {
        if (item.atraso && item.atraso > 0) {
          return {
            backgroundColor: "red",
            color: "white",
            textAlign: "center",
          };
        }
        return {
          backgroundColor: "white",
          color: "black",
          textAlign: "center",
        };
      },
      formatter: (value) => String(value).padStart(3, "0"),
    },
    { key: "nr_Plano", label: "Plano" },
    {
      key: "nome_Tipo_Parcela",
      label: "Tp. Parcela",
    },
    {
      key: "dt_Vencimento",
      label: "Vencimento",
      formatter: (value) => formatDate(value),
    },
    {
      key: "vl_Saldo",
      label: "Valor Saldo",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "vl_Saldo_Atualizado",
      label: "Valor Total",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "valorNegociado",
      label: "Valor Negociado",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "dt_Pgto",
      label: "Data Pagamento",
      defaultValue: SELECTED_DATE ? formatDate(SELECTED_DATE) : "---",
    },
    { key: "atraso", label: "Atraso", formatter: (value) => value.toString() },
  ];

  const renderTextLines = (item = null) => {
    if (item !== null) {
      return fieldMappings.map((mapping) =>
        buildText(mapping.label, item?.[mapping.field])
      );
    }
    return fieldMappings.map((mapping) =>
      buildText(mapping.label, data[0]?.[mapping.field])
    );
  };
  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;
  const parcelasAbertas =
    contratosAtivos === null || contratosAtivos === undefined
      ? []
      : contratosAtivos?.flatMap((item) =>
          item.parcelas.filter(
            (pItem) =>
              pItem.status === "A" &&
              !pItem.nr_Acordo /* && pItem.atraso > 0  ||
    (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
          )
        );
  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : null;

  // const handleContratoChange = (selection) => {
  //   setSelectedContrato(selection);
  // };
  const [checkedAll, setCheckedAll] = useState(false);

  const handleSelectAll = (e) => {
    tableData.map((item) => {
      if (
        item.nrContrato?.replaceAll(" ", "") ===
        selectedContract?.replaceAll(" ", "")
      )
        item.parcelaSelecionada = e.target.checked;
      else if (selectedContract === "")
        item.parcelaSelecionada = e.target.checked;
      return item;
    });

    const vlMinimo = tableData
      .filter((x) => x.parcelaSelecionada)
      .reduce((a, b) => a + b.vlAtualizadoDescontoMax, 0);
    // setValorMinimo(vlMinimo);
    // const totalNeg = data.reduce((a, b) => a + b.vlTotal, 0);
    // setValid(totalNeg >= vlMinimo);

    setTableData(tableData);
    setCheckedAll(e.target.checked);
  };

  const handleCalculoSuccess = (response) => {
    const neg = response.negociacaoDto[0] ?? null;
    const parcelas = neg?.parcelas;
    if (parcelas === undefined || parcelas === null) return [];
    const obj = parcelas
      // .filter((item) => {
      //   const parcAb = parcelasAbertas.find(
      //     (x) => x.id_Parcela === item.idParcela
      //   );
      //   return item.atraso > 0 || parcAb?.nome_Tipo_Parcela === "DIF_PARCELAS";
      // })
      .map((item) => {
        const parcAb = parcelasAbertas.find(
          (x) => x.id_Parcela === item.idParcela
        );
        item.parcelaSelecionada = false;
        return {
          ...item,
          nome_Tipo_Parcela: parcAb?.nome_Tipo_Parcela,
          dt_Pgto: parcAb?.dt_Pgto,
        };
      });

    const honorariosNegociacao = obj
      ? obj.reduce(
          (total, item) => Math.round((total + item.vlHoNegociado) * 100) / 100,
          0
        )
      : 0;
    const custasNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlDespesasNegociado) * 100) / 100,
          0
        )
      : 0;
    const notificacaoNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlNotificacaoNegociado) * 100) / 100,
          0
        )
      : 0;
    const tarifaNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlTarifaNegociado) * 100) / 100,
          0
        )
      : 0;
    const iofNegociacao = 0;
    const totalNegociacao = obj
      ? obj.reduce(
          (total, item) => Math.round((total + item.vlAtualizado) * 100) / 100,
          0
        )
      : 0;

    const subTotalNegociacao = obj
      ? totalNegociacao -
        honorariosNegociacao -
        custasNegociacao -
        notificacaoNegociacao -
        tarifaNegociacao -
        iofNegociacao
      : 0;
    let percHo = parseFloat(
      ((honorariosNegociacao * 100) / subTotalNegociacao).toFixed(2)
    );

    // const honor = obj
    // .filter((item) => item.parcelaSelecionada)
    // .reduce((a, b) => a + b.vlHoNegociado, 0);
    const honor = vlDesejado * (percHo / 100);
    if (isNaN(percHo)) percHo = 0;
    if (percHo > 100) percHo = 100;
    if (percHo < 0) percHo = 0;
    setPercHO(percHo);
    setTableData(obj);
  };
  async function getTiposOcorrencia() {
    setLoading(true);
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    if (tiposOcorrencia !== null && tiposOcorrencia !== undefined) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.cod_Ocorr_Sistema + " - " + item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];
      setOptionsOcorrencia(options);
    }
    setLoading(false);
  }

  const getCalculoNewcon = async () => {
    const data = [];
    for (const contrato of contratosAtivos) {
      const payload = {
        nrContratos: contrato.numero_Contrato,
        cpfCnpjDevedor: financiadoData.cpfCnpj,
      };
      const calculo = await postApi(payload, "getNewconParcelas");
      data.push(
        ...(calculo?.data?.envelope?.element?.element?.parcelasVincendas?.map(
          (x) => {
            x.vencida = false;
            return x;
          }
        ) ?? [])
      );
    }
    setTableDataNewcon(
      data.map((x) => {
        x.parcelaSelecionada = false;
        return x;
      })
    );
  };
  const emptyFunc = () => {};
  const asyncFunc = async () => {
    // busca Calculo Datacob
    cleanCalculoPost(
      [],
      0,
      0,
      new Date(),
      handleCalculoSuccess,
      emptyFunc,
      emptyFunc,
      () => {
        setLoadingDatacob(false);
      }
    );
    // busca Calculo NewCon
    getCalculoNewcon();
  };
  useEffect(() => {
    getTiposOcorrencia();
    const optPhone = clientData?.telefones.map((item) => {
      return {
        label: item.ddd + item.fone,
        value: item.ddd + item.fone,
      };
    });
    setOptionsPhone(optPhone);
    const optEmail = clientData?.emails?.map((item) => {
      return {
        label: item.endereco_Email,
        value: item.endereco_Email,
      };
    });
    setOptionsEmail(optEmail);

    asyncFunc();
  }, []);

  useEffect(() => {
    const numerosParcelasDatacob = tableData
      .filter((x) => x.parcelaSelecionada)
      ?.map((x) => x.nrParcela);
    // ?.join(", ");
    const numerosParcelasNewcon = tableDataNewcon
      .filter((x) => x.parcelaSelecionada)
      ?.map((x) => x.noParcela);
    //?.join(", ");
    const numerosParcelas = [
      ...numerosParcelasDatacob,
      ...numerosParcelasNewcon,
    ].join(", ");

    setNumerosParcelasSelecionadas(numerosParcelas);
  }, [tableData, tableDataNewcon]);

  const handleVlDesejadoChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");
    let value = 0;
    value = Number(input) / 100;
    if (input.length > 11) return;
    setVlDesejado(value);
  };
  const HandleInstallmentNewconChange = (input, item) => {
    setTableDataNewcon(
      tableDataNewcon.map((x) => {
        if (
          x.nrContrato === item.nrContrato &&
          x.noParcela === item.noParcela &&
          x.noPlano === item.noPlano
        ) {
          x.parcelaSelecionada = input.target.checked;
        }
        return x;
      })
    );
    setCheckedAllNewcon(
      tableDataNewcon
        .filter((x) =>
          selectedContratoNewcon?.value !== null &&
          selectedContratoNewcon?.value !== undefined
            ? x?.nrContrato?.replaceAll(" ", "") ===
              selectedContratoNewcon?.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const COLUMNS_NEWCON = [
    {
      key: "",
      label: "",
      formatterByObject: (item) => (
        <input
          type="checkbox"
          checked={item.parcelaSelecionada}
          onChange={(input) => HandleInstallmentNewconChange(input, item)}
        />
      ),
    },
    { key: "nrContrato", label: "Nr. Contrato" },
    {
      key: "noParcela",
      label: "No. Parcela",
      formatter: (value) => String(value).padStart(3, "0"),
    },
    {
      key: "noPlano",
      label: "No. Plano",
      formatter: (value) => String(value).padStart(3, "0"),
    },
    {
      key: "vlOriginal",
      label: "Vl. Original",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "vlSaldo",
      label: "Vl. Saldo",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "dtVencimento",
      label: "Dt. Vencimento",
      formatter: (value) => formatDate(value),
    },
  ];

  const handleVlEntradaChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");
    let value = 0;
    value = Number(input) / 100;
    if (input.length > 11) return;
    setVlEntrada(value);
  };
  const handleChangeSelectContract = (e) => {
    setSelectedContract(e.target.value);
    setCheckedAll(
      tableData
        .filter((x) =>
          e.target.value !== null &&
          e.target.value !== undefined &&
          e.target.value !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              e.target.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const HandleInstallmentDatacobChange = (input, item) => {
    const selec = tableData.map((x) => {
      if (x.idParcela === item.idParcela)
        x.parcelaSelecionada = input.target.checked;
      return x;
    });
    setTableData(selec);

    const vlMinimo = selec
      .filter((x) => x.parcelaSelecionada)
      .reduce((a, b) => a + b.vlAtualizadoDescontoMax, 0);

    setCheckedAll(
      tableData
        .filter((x) =>
          selectedContract !== null &&
          selectedContract !== undefined &&
          selectedContract !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              selectedContract?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };
  const calcTotalValue = (item) => {
    return (
      item.vlOriginal +
      item.vlHoOriginal +
      item.vlJurosOriginal +
      item.vlMultaOriginal +
      item.vlComPermanenciaOriginal +
      item.vlDespesasOriginal +
      item.vlNotificacaoOriginal +
      item.vlTarifaOriginal
    );
  };
  const handleChangeSelectedContractNewcon = (item) => {
    setSelectedContratoNewcon(item);
    setCheckedAllNewcon(
      tableDataNewcon
        .filter((x) =>
          item?.value !== null && item?.value !== undefined
            ? x?.nrContrato?.replaceAll(" ", "") ===
              item?.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const handleSelectAllNewcon = (e) => {
    setTableDataNewcon(
      tableDataNewcon.map((x) => {
        if (
          selectedContratoNewcon?.value !== null &&
          selectedContratoNewcon?.value !== undefined &&
          x?.nrContrato?.replaceAll(" ", "") ===
            selectedContratoNewcon?.value?.replaceAll(" ", "")
        )
          x.parcelaSelecionada = e.target.checked;
        else if (
          selectedContratoNewcon?.value === null ||
          selectedContratoNewcon?.value === undefined
        )
          x.parcelaSelecionada = e.target.checked;
        return x;
      })
    );
    setCheckedAllNewcon(e.target.checked);
  };

  const [currentTab, setCurrentTab] = useState("DataCob");

  const handleTabSelect = (tab) => {
    setCurrentTab(tab.label);
  };
  const tabs = [
    {
      id: 1,
      label: "DataCob",
      icon: "cil-spreadsheet",
      content: isLoadingDatacob ? (
        <div className="mt-5">
          <LoadingComponent />
        </div>
      ) : (
        <TableInstallment
          columns={COLUMNS}
          selectAll={checkedAll}
          selectedDate={SELECTED_DATE}
          selectedContract={selectedContract}
          contratosAtivos={contratosAtivos}
          tableData={tableData}
          handleSelectAll={handleSelectAll}
          handleChangeSelectContract={handleChangeSelectContract}
          calcTotalValue={calcTotalValue}
          HandleInstallmentChange={HandleInstallmentDatacobChange}
        />
      ),
    },

    {
      id: 2,
      label: "NewCon",
      icon: "cil-columns",
      content: isLoadingNewcon ? (
        <div className="mt-5">
          <LoadingComponent />
        </div>
      ) : (
        <>
          <Select
            className="mt-3"
            placeholder="Contratos"
            options={[
              { label: "Contratos", value: null },
              ...contratosAtivos.map((x) => ({
                label: x.numero_Contrato,
                value: x.numero_Contrato,
              })),
            ]}
            value={selectedContratoNewcon}
            getOptionValue={(option) => option?.value}
            getOptionLabel={(option) => option?.label}
            onChange={handleChangeSelectedContractNewcon}
          />
          <NewconTableInstallment
            HandleInstallmentChange={HandleInstallmentNewconChange}
            columns={COLUMNS_NEWCON}
            handleSelectAll={handleSelectAllNewcon}
            selectAll={checkedAllNewcon}
            tableData={tableDataNewcon.filter((x) =>
              selectedContratoNewcon?.value !== null &&
              selectedContratoNewcon?.value !== undefined
                ? x?.nrContrato?.replaceAll(" ", "") ===
                  selectedContratoNewcon?.value?.replaceAll(" ", "")
                : true
            )}
          />
        </>
      ),
    },
  ];
  const handleSaveOccurrence = async () => {
    if (selectedPhone === null || selectedOcorrencia === null) {
      toast.warning("Selecione o telefone e a ocorrência!");
      return;
    }
    toast.info("Enviando ocorrência...");
    const payload = {
      login: user?.username,
      id_Contrato: financiadoData?.id_Contrato,
      id_Ocorrencia_Sistema: selectedOcorrencia?.value,
      observacao: textData.current.innerText,
      telefones: [selectedPhone?.value],
      complemento: "",
      telefoneParaRetorno: selectedPhone?.value,
      ...(financiadoData?.idLinkedGroup &&
        financiadoData?.idGrupo && {
          groupId: financiadoData?.idGrupo,
          linkedGroupId: financiadoData?.idLinkedGroup,
        }),
    };
    const ocorrencia = await POST_DATA(
      "Datacob/historicoAdicionar",
      payload,
      false,
      true
    );
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
    } else {
      toast.error("Erro ao enviar ocorrência!");
    }
  };
  const handleCloseClick = () => {
    onClose();
  };
  const handleOcorrenciaChange = (event) => {
    setSelectedOcorrencia(event);
  };

  const handlePhoneChange = (event) => {
    setSelectedPhone(event);
  };
  const calcDemaisParcelas = () => {
    if (vlEntrada === 0 || qtdParcela <= 1) return "";
    return (
      (data.reduce((a, b) => a + b.vlTotal, 0) - vlEntrada) / (qtdParcela - 1)
    );
  };

  return (
    <div>
      <CRow>
        <CCol md="8">
          <CRow>
            <CCol className="container-fluid ">
              <CCard>
                <CTabs onSelect={handleTabSelect} activeTab={"DataCob"}>
                  <CNav className="custom-nav">
                    {tabs.map((tab) => (
                      <CNavItem
                        key={tab.id}
                        className={
                          currentTab === tab.label ? "" : "nonactive-tab"
                        }
                      >
                        <CNavLink
                          data-tab={tab.label}
                          onClick={() => handleTabSelect(tab)}
                        >
                          <i className={tab.icon} /> {tab.label}
                        </CNavLink>
                      </CNavItem>
                    ))}
                  </CNav>
                  <CTabContent
                    className="px-3 overflow-auto"
                    style={{
                      maxHeight: "230px",
                      minHeight: "200px",
                    }}
                  >
                    {tabs.map((tab) => (
                      <CTabPane key={tab.id} data-tab={tab.label}>
                        {tab.content}
                      </CTabPane>
                    ))}
                  </CTabContent>
                </CTabs>
              </CCard>
            </CCol>
          </CRow>
          <CRow>
            <CCol>
              <CLabel>Valor minimo (régua)</CLabel>
              <br />
              <CLabel className="text-danger">
                <strong>{formatCurrency(valorMinRegua ?? 0, false)}</strong>
              </CLabel>
            </CCol>
            <CCol>
              <CLabel>Valor máximo (régua)</CLabel>
              <br />
              <CLabel className="text-success">
                <strong>{formatCurrency(valorMaxRegua ?? 0, false)}</strong>
              </CLabel>
            </CCol>
            <CCol>
              <CLabel>Valor desejado</CLabel>
              <CInput
                value={formatCurrency(vlDesejado ?? 0, false)}
                onChange={handleVlDesejadoChange}
              />
            </CCol>
            <CCol>
              <CLabel>Valor entrada</CLabel>
              <CInput
                value={formatCurrency(vlEntrada ?? 0, false)}
                onChange={handleVlEntradaChange}
              />
            </CCol>
          </CRow>

          <CRow>
            <CCol>
              <CLabel>Qtd. Parcelas (acordo)</CLabel>
              <CInput
                value={qtdParcela}
                type="number"
                onChange={(e) => setQtdParcela(e.target.value)}
              />
            </CCol>
            <CCol>
              <CLabel>Valor de H.O</CLabel>
              <CInput value={formatCurrency(vlHo ?? 0, false)} disabled />
            </CCol>
            <CCol>
              <CLabel>Parcelas Selecionadas</CLabel>
              <br />
              <CLabel className="">
                <strong>{numerosParcelasSelecionadas}</strong>
              </CLabel>
            </CCol>
          </CRow>
          <CRow className="mb-2 mt-2">
            <CCol>
              <CLabel clasName="text-nowrap">Negociador</CLabel>
              <CInput
                onChange={(e) => setNegociador(e.target.value)}
                value={negociador}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Ativo</CLabel>
              <CInput
                onChange={(e) => setAtivo(e.target.value)}
                value={ativo}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Status Projuris</CLabel>
              <CInput
                onChange={(e) => setStatusProjuris(e.target.value)}
                value={statusProjuris}
              />
            </CCol>
          </CRow>
          <CRow>
            <CCol>
              <CLabel clasName="text-nowrap">Dt. Primeira Parcela</CLabel>
              <br />
              <ReactDatePicker
                selected={new Date(primeiraParcela)}
                onChange={(e) => setPrimeirParcela(e.toJSON())}
                className="form-control"
                dateFormat="dd/MM/yyyy"
                onKeyDown={(e) => e.preventDefault()}
              />
            </CCol>

            <CCol>
              <CLabel>Dt. Demais Parcela</CLabel>
              <br />
              <ReactDatePicker
                selected={new Date(demaisParcelas)}
                onChange={(e) => setDemaisParcelas(e.toJSON())}
                className="form-control"
                dateFormat="dd/MM/yyyy"
                onKeyDown={(e) => e.preventDefault()}
              />
            </CCol>
            <CCol>
              <CLabel>E-mail do cliente</CLabel>
              <Select
                value={email}
                options={optionsEmail}
                onChange={(e) => setEmail(e)}
                placeholder={"Selecione"}
              />
            </CCol>
          </CRow>

          <CRow>
            <CCol>
              <CButton
                color="info"
                onClick={getCalculo}
                className="my-2"
                block
                disabled={isLoading}
              >
                Calcular
              </CButton>
            </CCol>
          </CRow>
        </CCol>
        <CCol md="4">
          <CCard>
            {" "}
            {isLoading ? (
              <CardLoading />
            ) : (
              <CCardBody>
                <div
                  className="d-flex flex-column"
                  style={{ maxHeight: "439px", overflowY: "auto" }}
                  ref={textData}
                >
                  <div>
                    <div>
                      <strong>Valor desejado:</strong>{" "}
                      {formatCurrency(vlDesejado, false)}
                    </div>
                    <div>
                      <strong>Qtd. Parcelas (acordo):</strong> {qtdParcela}
                    </div>
                    <div>
                      <strong>Valor de Entrada:</strong>{" "}
                      {formatCurrency(vlEntrada, false)}
                    </div>
                    <div>
                      <strong>Valor das parcela (acordo):</strong>{" "}
                      {formatCurrency(
                        (vlDesejado - vlEntrada) / qtdParcela,
                        false
                      )}
                    </div>
                    <div>
                      <strong>Valor de H.O:</strong>{" "}
                      {formatCurrency(vlHo, false)}
                    </div>
                    <div>
                      <strong>Parcelas selecionadas:</strong>{" "}
                      {numerosParcelasSelecionadas}
                    </div>
                    <div>
                      <strong>Data da 1ª Parcela:</strong>{" "}
                      {formatDate(primeiraParcela)}
                    </div>
                    <div>
                      <strong>Data das Demais Parcela:</strong>{" "}
                      {formatDate(demaisParcelas)}
                    </div>
                    <div>
                      <strong>Negociador:</strong> {negociador}
                    </div>
                    <div>
                      <strong>Ativo:</strong> {ativo}
                    </div>
                    <div>
                      <strong>Status Projuris:</strong> {statusProjuris}
                    </div>

                    <div>
                      <strong>E-mail Cliente:</strong> {email?.value}
                    </div>
                  </div>
                  <div>
                    <div>
                      <strong>
                        Total Negociação:{" "}
                        {formatCurrency(
                          data.reduce((a, b) => a + b.vlTotal, 0),
                          false
                        )}
                      </strong>
                    </div>

                    {/* <div>
                      <strong>
                        Demais Parcelas (
                        {qtdParcela === 0 || vlEntrada === 0
                          ? " "
                          : qtdParcela - 1}
                        ):
                      </strong>{" "}
                      {formatCurrency(calcDemaisParcelas(), false)}
                    </div> */}
                  </div>
                  <br />
                  <div>
                    <strong>Detalhes Contratos</strong>
                  </div>
                  <br />
                  {data.map((item, index) => (
                    <>
                      <div key={index}>
                        {renderTextLines(item).map((line, index) => (
                          <div key={index}>{line}</div>
                        ))}
                      </div>
                      <br />
                    </>
                  ))}
                </div>
              </CCardBody>
            )}
          </CCard>
        </CCol>
      </CRow>
      <CCardFooter>
        <CRow>
          <CCol md="6">
            <CLabel className={"mt-2"}>Selecione a ocorrência:</CLabel>
            <Select
              value={selectedOcorrencia}
              options={optionsOcorrencia}
              onChange={handleOcorrenciaChange}
              placeholder={"Selecione"}
            />
          </CCol>
          <CCol md="6">
            <CLabel className={"mt-2"}>Selecione o telefone:</CLabel>
            <Select
              value={selectedPhone}
              options={optionsPhone}
              onChange={handlePhoneChange}
              placeholder={"Selecione"}
            />
          </CCol>
        </CRow>

        <CRow className="mt-4 text-center">
          <CCol>
            <CButton
              className={"mr-2"}
              color="success"
              onClick={handleSaveOccurrence}
            >
              Adicionar à ocorrência
            </CButton>
            <CButton
              className={"mr-2"}
              color="info"
              onClick={() => setShowSendEmailModal(true)}
            >
              Enviar E-mail
            </CButton>

            <CButton
              color="secondary"
              className="mr-2"
              onClick={handleCloseClick}
              disabled={isLoading}
            >
              Fechar
            </CButton>
          </CCol>
        </CRow>
      </CCardFooter>
      {showSendEmailModal && (
        <SendEmailModal
          show={showSendEmailModal}
          handleClose={() => setShowSendEmailModal(false)}
          msg={textData.current.innerText}
          em={email?.value}
        />
      )}
    </div>
  );
};

export default QuitacaoParcelada;
