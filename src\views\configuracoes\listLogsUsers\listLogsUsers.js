import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { CBadge, CButton, CCard, CCardBody, CCol, CDataTable, CForm, CInput, CLabel, CRow } from "@coreui/react";
import { getURI } from "src/config/apiConfig";
import { formatDateTime, postManager } from "src/reusable/helpers";
import CardLoading from "src/reusable/CardLoading";
import { useHistory } from "react-router-dom/cjs/react-router-dom.min";
import { getApi } from "src/reusable/functions";
import LoadingComponent from "src/reusable/Loading.js";
import { useAuth } from "src/auth/AuthContext";

const ListLogsUsers = () => {
  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [loadingExport, setLoadingExport] = useState(false);
  const [selectNome, setSelectNome] = useState([]);
  const [selectLogin, setSelectLogin] = useState([]);

  const { authToken } = useAuth();

  const [filters, setFilters] = useState({
    user_name: "",
    name: "",
    data_inicial: "",
    data_final: "",
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFilters({
      ...filters,
      [name]: value,
    });
  };
  const handleInputLoginChange = (e) => {
    setFilters({
      ...filters,
      user_name: e.target.value,
    });
  };
  const handleInputNomeChange = (e) => {
    setFilters({
      ...filters,
      name: e.target.value,
    });
  };

  const handleSearch = async () => {
    await getListaLogsUsers(false);
  };

  const tipoBadge = (item) => {
    if (item === "login") return <CBadge color="success">LOGIN</CBadge>;
    return <CBadge color="danger">LOGOUT</CBadge>;
  };

  const liberarUsuario = async (item) => {
    const params = { userName: item?.userName, ipUser: item?.ipUser };
    const rawResponse = await getApi(params, "getAuthResetUser");   
     
    if (rawResponse.length)
      toast.success("Liberado "+ rawResponse);
  }

  const handleViewActions = (item) => {
    return <CButton
          color="warning"
          onClick={() => liberarUsuario(item)}
          title='Liberar Acesso'
        >
          <i className="cil-reload"></i>
        </CButton>
  };

  const columns = [
    {
      key: "userName",
      label: "Login",
    },
    {
      key: "name",
      label: "Nome",
    },
    {
      key: "ipUser",
      label: "Ip de Login",
    },
    {
      key: "type",
      label: "Tipo",
    },
    {
      key: "responseApi",
      label: "Motivo Falha",
    },
    {
      key: "createdAt",
      label: "Data",
      formatter: (item) => formatDateTime(item),
    },
    {
      key: "actions",
      label: "Liberar"
    },
  ];

  const handleExportExcel = async (e) => {
    if (data?.length === 0) {
      toast.error("Nenhum Registro para Exportar!");
      return;
    }
    setLoadingExport(true);
    try {
      const url = getURI("postListLogUserExcel");
      const response = await fetch(url, {
        method: "POST",
        headers: {
          "content-type": "application/json",
          Authorization: "Bearer " + window.localStorage.getItem("token"),
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = url;
        link.download = "dados.xlsx";
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
      } else {
        toast.warning("Nenhum resultado encontrado!");
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingExport(false);
    }
  };

  const uniqueArray = (array, key, label) => {
    const uniqueArray = array.reduce((acc, value) => {
      if (!acc.includes(value[key])) {
        acc.push(value[key]);
      }
      return acc;
    }, []);

    return [
      { value: null, label: label },
      ...uniqueArray.map((item) => ({
        value: item,
        label: item,
      })),
    ];
  };

  async function getListaLogsUsers(set = true) {
    setIsLoading(true);
    try {
      const params = {
        user_name: filters.user_name ?? "",
        name: filters.name ?? "",
        data_inicial: filters.data_inicial,
        data_final: filters.data_final,
      };
      const listaLogsUsers = await getApi(params, "getAuthLogUser");

      if (listaLogsUsers) {
        const listaDecript = await Promise.all(
          listaLogsUsers.map(async (user) => {
            user.userName = await postManager(authToken, user.userName, 2);
            user.name = await postManager(authToken, user.name, 2);
            return user;
          })
        );
        setData(listaDecript);
        if (set) {
          setSelectLogin(uniqueArray(listaLogsUsers, "userName", "Login"));
          setSelectNome(uniqueArray(listaLogsUsers, "name", "Nome"));
        }
      }
    } catch (e) {
      setData([]);
    }
    setIsLoading(false);
    return;
  }

  const history = useHistory();

  useEffect(() => {
    const isAdmin = JSON.parse(localStorage.getItem("user")).isAdmin;
    if (!isAdmin) {
      history.push("/telaprincipal");
    }
  }, []);

  return (
    <div>
      <h3>Log de autenticação dos Usuários:</h3>
      <p style={{ color: "gray" }}></p>
      <div>
        <CCard>
          <CCardBody>
            <CRow>
              <CCol md={2}>
                <CLabel>Data Inicial</CLabel>
                <CInput
                  type="date"
                  name="data_inicial"
                  value={filters.data_inicial}
                  onChange={handleInputChange}
                />
              </CCol>
              <CCol md={2}>
                <CLabel>Data Final</CLabel>
                <CInput
                  type="date"
                  name="data_final"
                  value={filters.data_final}
                  onChange={handleInputChange}
                />
              </CCol>
              <CCol md={3}>
                <CLabel>Login</CLabel>
                <CInput
                  type="text"
                  name="user_name"
                  placeholder="Login"
                  value={filters.user_name}
                  onChange={handleInputLoginChange}
                />
              </CCol>
              <CCol md={3}>
                <CLabel>Nome</CLabel>
                <CInput
                  type="text"
                  name="name"
                  placeholder="Nome"
                  value={filters.name}
                  onChange={handleInputNomeChange}
                />
              </CCol>
              <CCol md={2} className="d-flex align-align-items-end mt-4">
                <CButton color="primary" onClick={handleSearch}>
                  Buscar
                </CButton>
              </CCol>
            </CRow>
          </CCardBody>
        </CCard>
        <CCard>
          <CCardBody>
            <CForm>
              <CRow>
                <CCol xs>
                  <CRow>
                    {isLoading ? (
                      <CardLoading />
                    ) : (
                      <CCol>
                        <CDataTable
                          items={data}
                          fields={columns}
                          
                          scopedSlots={{
                            createdAt: (item) => (
                              <td>{formatDateTime(item?.createdAt)}</td>
                            ),
                            type: (item) => <td>{tipoBadge(item?.type)}</td>,
                            actions: (item) => <td>{handleViewActions(item)}</td>,
                          }}
                          pagination
                          // sorter
                          // columnFilter
                          itemsPerPage={10}
                          heightParam="600px"
                        />
                        <div className="d-flex justify-content-end mt-3">
                          <CButton
                            color="primary"
                            onClick={handleExportExcel}
                          >
                            {!loadingExport && "Exportar para Excel"}
                            {loadingExport && <LoadingComponent />}
                          </CButton>
                        </div>
                      </CCol>
                    )}
                  </CRow>
                </CCol>
              </CRow>
            </CForm>
          </CCardBody>
        </CCard>
      </div>
    </div>
  );
};

export default ListLogsUsers;
