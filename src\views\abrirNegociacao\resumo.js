import React, { useState, useEffect } from "react";
import {
  CCard,
  CCardBody,
  CCol,
  CDataTable,
  CRow,
  CButton,
  CInputCheckbox,
  CBadge,
} from "@coreui/react";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import { GET_DATA } from "src/api";

const NegociacaoResumo = () => {
  const negociacoes = localStorage.getItem("negociacoes")
    ? JSON.parse(localStorage.getItem("negociacoes"))
    : "";

  const financiadoData = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const [negociacoesData, setNegociacoesData] = useState(null);
  const [parcelasTable, setParcelasData] = useState(null);

  const [selectedRow, setSelectedRow] = useState(null);
  const [selectedStatus, setSelectedStatus] = useState("");

  const negociacoesColumns = [
    { key: "id_Negociacao", label: "ID" },
    { key: "status" },
    { key: "liberado" },
    { key: "descricao" },
    {
      key: "dt_Negociacao",
      label: "Data Negociação",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "vl_Negociacao", label: "Valor" },
    {
      key: "negociacao_Enviada",
      label: "Prop. Enviada",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dt_Cadastro_Negociacao",
      label: "Data Inclusão",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "usuario" },
    {
      key: "dt_Liberacao",
      label: "Data Liberação",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "usuario_Liberou",
      label: "Usuário Liberação",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "usuario_Bloqueou",
      label: "Usuário Bloq.",
      _style: { whiteSpace: "nowrap" },
    },
  ];

  const parcelasColumns = [
    { key: "numero_Contrato", label: "Contrato" },
    {
      key: "dt_Vencimento",
      label: "Data Vencimento",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "nr_Parcela", label: "Parcela" },
    { key: "nr_Plano", label: "Plano" },
    { key: "tipo_Parcela", _style: { whiteSpace: "nowrap" } },
    {
      key: "id_Faixa_Calculo",
      label: "Calc. ID",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "vl_Principal",
      label: "Valor Principal",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "vl_Total", label: "Valor Total", _style: { whiteSpace: "nowrap" } },
    {
      key: "vl_Honorario",
      label: "Valor Honorários",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "percentual_Honorario",
      label: "% Honor.",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "vl_Desconto",
      label: "Valor Desconto",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "percentual_Desconto",
      label: "% Desc.",
      _style: { whiteSpace: "nowrap" },
    },
    { key: "nrAtraso", label: "Atraso" },
    {
      key: "vl_Desconto_Excedido",
      label: "Valor Desconto Excedido",
      _style: { whiteSpace: "nowrap" },
    },
  ];

  async function getParcelas(id_Negociacao) {
    const data = {
      IdNegociacao: id_Negociacao,
      numeroContrato: financiadoData.numero_Contrato,
      ...(financiadoData?.idLinkedGroup &&
        financiadoData?.idGrupo && {
          groupId: financiadoData?.idGrupo,
          linkedGroupId: financiadoData?.idLinkedGroup,
        }),
    };
    const parcelas = await GET_DATA("Datacob/Negociacoes/Parcelas", data);
    setParcelasData(parcelas);
    return parcelas;
  }

  const handleClick = async (item) => {
    await getParcelas(item.id_Negociacao);
    setSelectedRow(item.id_Negociacao);
  };

  const renderBadge = (status) => {
    switch (status) {
      case "P":
        return (
          <td>
            <CBadge color="success">Pago</CBadge>
          </td>
        );
      case "A":
        return (
          <td>
            <CBadge color="info">Aberto</CBadge>
          </td>
        );
      case "C":
        return (
          <td>
            <CBadge color="danger">Cancelado</CBadge>
          </td>
        );
      default:
        break;
    }
  };

  const rowClassName = (item) => {
    return item.id_Negociacao === selectedRow ? "selected-row" : "";
  };

  useEffect(() => {
    const data = { ...negociacoes };
    const dataTable = Object.keys(data).reduce((acc, key) => {
      const negociacao = data[key];
      const formattedValue = formatCurrency(negociacao.vl_Negociacao, false);
      const formattedNegociacao = {
        ...negociacao,
        vl_Negociacao: formattedValue,
      };
      return { ...acc, [key]: formattedNegociacao };
    }, {});

    setNegociacoesData(negociacoes ? dataTable : negociacoes);

    if (negociacoes) {
      getParcelas(negociacoes[0].id_Negociacao);
      setSelectedRow(negociacoes[0].id_Negociacao);
    }
  }, []);

  useEffect(() => {
    if (negociacoes) {
      const filteredData = negociacoes.filter((item) => {
        if (selectedStatus == "AC")
          return (
            item.id_Negociacao !== undefined &&
            item.id_Negociacao &&
            item.id_Negociacao > 0 &&
            item.status === "A"
          );

        const matchesStatus = !selectedStatus || item.status === selectedStatus;
        return matchesStatus;
      });
      const format = filteredData.map((item) => {
        const formattedValue = formatCurrency(item.vl_Negociacao, false);
        const formattedNegociacao = { ...item, vl_Negociacao: formattedValue };
        return formattedNegociacao;
      });
      setNegociacoesData(format);
    }
  }, [selectedStatus]);

  return (
    <div>
      <CRow className="my-2">
        <CCol className="mx-3">
          <CButton
            color={selectedStatus === "" ? "info" : "secondary"}
            className="mr-2"
            onClick={() => setSelectedStatus("")}
          >
            Todos
          </CButton>
          <CButton
            color={selectedStatus === "A" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("A")}
          >
            Aberto
          </CButton>
          <CButton
            color={selectedStatus === "P" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("P")}
          >
            Pago
          </CButton>
          <CButton
            color={selectedStatus === "C" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("C")}
          >
            Cancelado
          </CButton>

          <CButton
            color={selectedStatus === "AC" ? "info" : "secondary"}
            size="sm"
            className="mr-2"
            onClick={() => setSelectedStatus("AC")}
          >
            Acordos
          </CButton>
        </CCol>
      </CRow>
      <div className="container-fluid">
        <CCard className="mb-2">
          <div style={{ maxHeight: "300px", overflowX: "auto" }}>
            <div style={{ width: "fit-content", overflowY: "auto" }}>
              <CDataTable
                items={negociacoesData}
                fields={negociacoesColumns}
                sorter
                addTableClasses={[
                  "fixed-header-datatable",
                  "overflow-body-datatable",
                ]}
                scopedSlots={{
                  id_Negociacao: (item) => (
                    <td className={rowClassName(item)}>
                      <CButton
                        onClick={() => handleClick(item)}
                        className="flat py-0 px-0 button-link"
                      >
                        <strong>{item.id_Negociacao}</strong>
                      </CButton>
                    </td>
                  ),
                  status: (item) => renderBadge(item.status),
                  descricao: (item) => (
                    <td className="nowrap-cell">{item.descricao}</td>
                  ),
                  dt_Negociacao: (item) =>
                    item.dt_Negociacao ? (
                      <td>{formatDate(item.dt_Negociacao)}</td>
                    ) : (
                      <td>---</td>
                    ),
                  dt_Liberacao: (item) =>
                    item.dt_Liberacao ? (
                      <td>{formatDate(item.dt_Liberacao)}</td>
                    ) : (
                      <td>---</td>
                    ),
                  dt_Cadastro_Negociacao: (item) =>
                    item.dt_Cadastro_Negociacao ? (
                      <td>{formatDate(item.dt_Cadastro_Negociacao)}</td>
                    ) : (
                      <td>---</td>
                    ),
                  negociacao_Enviada: (item) => (
                    <td style={{ textAlign: "center", paddingLeft: "40px" }}>
                      <CInputCheckbox
                        defaultChecked={item.negociacao_Enviada}
                      />
                    </td>
                  ),
                  liberado: (item) =>
                    item.liberado ? (
                      <td style={{ textAlign: "center", paddingLeft: "40px" }}>
                        <CInputCheckbox defaultChecked={item.liberado} />
                      </td>
                    ) : (
                      <td>---</td>
                    ),
                  usuario_Liberou: (item) =>
                    item.usuario_Liberou ? (
                      <td>{item.usuario_Liberou}</td>
                    ) : (
                      <td>---</td>
                    ),
                  usuario_Bloqueou: (item) =>
                    item.usuario_Bloqueou ? (
                      <td>{item.usuario_Bloqueou}</td>
                    ) : (
                      <td>---</td>
                    ),
                }}
              />
            </div>
          </div>
        </CCard>
        <CCard>
          <div style={{ maxHeight: "260px", overflowX: "auto" }}>
            <CDataTable
              items={parcelasTable}
              fields={parcelasColumns}
              addTableClasses={[
                "fixed-header-datatable",
                "overflow-body-datatable",
              ]}
              scopedSlots={{
                numero_Contrato: (item) => (
                  <td className="nowrap-cell">{item.numero_Contrato}</td>
                ),
                dt_Vencimento: (item) =>
                  item.dt_Vencimento ? (
                    <td>{formatDate(item.dt_Vencimento)}</td>
                  ) : (
                    <td>---</td>
                  ),
                vl_Desconto: (item) =>
                  item.vl_Desconto ? (
                    <td>{formatThousands(item.vl_Desconto)}</td>
                  ) : (
                    <td>---</td>
                  ),
                vl_Principal: (item) =>
                  item.vl_Principal ? (
                    <td>{formatThousands(item.vl_Principal)}</td>
                  ) : (
                    <td>---</td>
                  ),
                vl_Total: (item) =>
                  item.vl_Total ? (
                    <td>{formatThousands(item.vl_Total)}</td>
                  ) : (
                    <td>---</td>
                  ),
                percentual_Honorario: (item) =>
                  item.percentual_Honorario ? (
                    <td>{formatThousands(item.percentual_Honorario)}</td>
                  ) : (
                    <td>---</td>
                  ),
                percentual_Desconto: (item) =>
                  item.percentual_Desconto ? (
                    <td>{formatThousands(item.percentual_Desconto)}</td>
                  ) : (
                    <td>---</td>
                  ),
              }}
            />
          </div>
        </CCard>
      </div>
    </div>
  );
};

export default NegociacaoResumo;
