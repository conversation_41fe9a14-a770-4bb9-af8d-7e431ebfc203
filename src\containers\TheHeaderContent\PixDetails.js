import React from "react";
import { CRow, CCol } from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import {
  formatCurrency,
  formatDate,
  formatDocument,
} from "src/reusable/helpers.js";

const PixDetails = ({ tickets }) => {
  const statusPix = (status) => {
    if (status === "C") {
      return "Cancelado";
    } else if (status === "P") {
      return "Pago";
    } else if (status === "A") {
      return "Aberto";
    }
    return status;
  };

  return (
    <CCol className={"p-4"}>
      <CRow>
        <div
          className="table-responsive"
          style={{ overflow: "auto", maxHeight: "500px" }}
        >
          <table className="table">
            <thead>
              <tr>
                <th>CPF/CNPJ do Financiado</th>
                <th>Nome do Financiado</th>
                <th>Contrato do Financiado</th>
                <th>Valor</th>
                <th>Dt. Pix</th>
                <th>Status</th>
                <th>Cliente</th>
                <th>Grupo</th>
                <th>Operador</th>
              </tr>
            </thead>
            <tbody>
              {tickets?.length < 1 && (
                <tr>
                  <td colspan="7" className="text-center">
                    Nenhum Pix Encontrado!
                  </td>
                </tr>
              )}
              {tickets?.map((item, index) => {
                return (
                  <tr key={index}>
                    <td>{formatDocument(item.cpfCnpj)}</td>
                    <td>{item.nomeFinanciado}</td>
                    <td>{item.nrContrato}</td>
                    <td>{formatCurrency(item.valor)}</td>
                    <td>{formatDate(item.dataPix)}</td>
                    <td>{statusPix(item.statusPix)}</td>
                    <td>{item.nomeCliente}</td>
                    <td>{item.nomeGrupo}</td>
                    <td>{item.nomeOperador}</td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </CRow>
    </CCol>
  );
};

export default PixDetails;
