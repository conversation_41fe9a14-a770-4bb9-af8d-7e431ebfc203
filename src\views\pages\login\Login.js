import { useState, useEffect } from "react";
import { useHistory } from "react-router-dom";
import {
  CCard,
  CCardBody,
  CContainer,
  CRow,
  CCol,
  CForm,
  CFormGroup,
  CLabel,
  CInput,
  CButton,
} from "@coreui/react";
import { getURI } from "../../../config/apiConfig";
import { toast } from "react-toastify";
import LoadingComponent from "src/reusable/Loading";
import RamalModal from "src/views/tactium/RamalModal";
import OlosFirstLoginModal from "src/views/telefonia/OlosFirstLoginModal";
import { useAuth } from "src/auth/AuthContext";
import CrmAuthModal from "src/views/telaPrincipal/Modal/CrmAuthModal.tsx";
import { POST_DATA } from "src/api";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";
import { postManager } from "src/reusable/helpers.js";

const Login = () => {
  const {
    loginAuthContext,
    logoutAuthContext,
    loginAuthRefresh,
    user,
    setUser,
    connection,
    setConfigUser,
  } = useAuth();
  const { activateTelephony, passCode } = useWebsocketTelefoniaContext();
  const history = useHistory();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [token, setToken] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const [showModalTactium, setShowModalTactium] = useState(false);
  const [showModalOlosFirst, setShowModalOlosFirstLogin] = useState(false);
  const [crmAuthModal, setCrmAuthModal] = useState(false);

  const [chosenCrm, setChosenCrm] = useState(null);
  const [checkUseTelefonia, setCheckUseTelefonia] = useState(true);

  const [firstAccess, setFirstAccess] = useState(false);
  const [idUserFirstAccess, setIdUserFirstAccess] = useState(null);

  const handleClick = (e) => {
    e.preventDefault();
    const login = {
      username: email,
      password: password,
      isPhone: checkUseTelefonia ? true : false,
    };
    authLogin(login);
  };

  const [senhaValidacao, setSenhaValidacao] = useState({
    hasUpperCase: false,
    hasLowerCase: false,
    hasNumber: false,
    hasSpecialChar: false,
    hasLength: false,
    matchPassword: false,
  });

  const handleFirstAcessClick = (e) => {
    e.preventDefault();

    const login = {
      id: idUserFirstAccess,
      password: newPassword,
    };
    patchFirstAccess(login);
  };

  useEffect(() => {
    const hasUpperCase = /[A-Z]/.test(newPassword);
    const hasLowerCase = /[a-z]/.test(newPassword);
    const hasNumber = /[0-9]/.test(newPassword);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);
    const hasLength = newPassword.length >= 8;
    const matchPassword = newPassword === confirmPassword;

    if (
      !hasUpperCase ||
      !hasLowerCase ||
      !hasNumber ||
      !hasSpecialChar ||
      !hasLength ||
      !matchPassword
    ) {
      setSenhaValidacao({
        hasUpperCase,
        hasLowerCase,
        hasNumber,
        hasSpecialChar,
        hasLength,
        matchPassword,
      });
      return;
    } else {
      setSenhaValidacao({
        hasUpperCase: true,
        hasLowerCase: true,
        hasNumber: true,
        hasSpecialChar: true,
        hasLength: true,
        matchPassword: true,
      });
    }
  }, [newPassword, confirmPassword]);

  const addLogLoginUser = async (tipo, responseApi, userName) => {
    try {
      const data = { type: tipo, responseApi: responseApi, userName: userName };
      await POST_DATA(getURI("authLogUser"), data, true);
    } catch (error) {
      console.error("Erro:", error);
    }
  };

  const authLogin = async (login) => {
    if (checkUseTelefonia) {
      activateTelephony();
    }

    logoutAuthContext();
    loginAuthRefresh();
    try {
      setIsLoading(true);
      const rawResponse = await fetch(`${getURI()}/Auth/Login`, {
        method: "POST",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        body: JSON.stringify(login),
      });

      const content = await rawResponse.json();
      await addLogLoginUser("login", content.message, login.username);
      if (rawResponse.ok) {
        if (content.success) {
          if (content.data?.firstAccess === true) {
            setIdUserFirstAccess(content.data?.user.id);
            setToken(content.data?.accessToken);
            setFirstAccess(true);
            setIsLoading(false);
          } else {
            loginAuthContext(content.data.accessToken);
            loginAuthRefresh(content.data.sessionToken);
            await getPermissionsAndGroups(content.data.user.id);
            await getUser(content.data.user.id);
          }
        } else {
          console.log("Erro de login");
          setIsLoading(false);
        }
      } else {
        alert("Usuário ou senha incorretos.");
        console.error("Erro:", rawResponse.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro:", error);
      setIsLoading(false);
    }
  };

  const patchFirstAccess = async (login) => {
    try {
      setIsLoading(true);
      const rawResponse = await fetch(`${getURI()}/Auth/UpdateFirstAccess`, {
        method: "PUT",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(login),
      });

      const content = await rawResponse.json();
      if (rawResponse.ok) {
        if (content.success) {
          const loginAuth = {
            username: email,
            password: newPassword,
            isPhone: checkUseTelefonia ? true : false,
          };
          authLogin(loginAuth);
        } else {
          console.log("Erro ao atualizar senha");
          setIsLoading(false);
        }
      } else {
        alert("Ocorreu um erro ao atualizar sua senha.");
        console.error("Erro:", rawResponse.statusText);
        setIsLoading(false);
      }
    } catch (error) {
      console.error("Erro:", error);
      setIsLoading(false);
    }
  };

  const getPermissionsAndGroups = async (userId) => {
    try {
      const token = localStorage.getItem("token");
      const response = await fetch(
        `${getURI()}/Auth/GetPermissionsAndGroups/${userId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          localStorage.setItem("authTokenPermissionsAndGroups", data.data);
          setConfigUser(data.data);
        }
      }
    } catch (error) {
      console.error("Erro ao buscar usuário:", error);
    }
  };
  const getUser = async (userId) => {
    const token = localStorage.getItem("token");
    try {
      const response = await fetch(
        `${getURI()}/User/${userId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
        (error) => {
          console.error("Erro ao buscar usuário:", error);
        }
      );

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          const [name, username, email] = await Promise.all([
            postManager(token, data.data.name, 2),
            postManager(token, data.data.username, 2),
            postManager(token, data.data.email, 2),
          ]);

          data.data.name = name;
          data.data.username = username;
          data.data.email = email;

          setUser(data.data);
          localStorage.setItem("user", JSON.stringify(data.data));
          setIsLoading(false);
          if (
            data.data &&
            data.data.telephonyId === 1 &&
            !JSON.parse(localStorage.getItem("ramalTactium")) //Provavelmente desncessário se isso ficar na tela de login
          ) {
            setShowModalTactium(true);
          } else if (
            data.data &&
            data.data.telephonyId === 2 &&
            (data.data.agentFone === null ||
              data.data.agentFone.username === "")
          ) {
            setShowModalOlosFirstLogin(true);
          } else if (verifyCrmAuth(data.data)) {
            setCrmAuthModal(true);
          } else {
            history.push("/telaprincipal");
            // window.location.reload();
          }
        } else {
          toast.warn(`Erro no login: ${data.message}`);
          setIsLoading(false);
          return;
        }
      } else {
        console.error("Erro:", response?.statusText);
      }
    } catch (error) {
      console.error("Erro buscando usuários:", error);
    }
  };

  const verifyCrmAuth = (data) => {
    if (data.datacobs.length === 1) {
      const crm = data.crmCred.find(
        (item) => item.crm === data.datacobs[0].datacobNumber
      );
      if (crm === undefined) {
        setChosenCrm(data.datacobs[0].datacobNumber);
        return true;
      }
      return false;
    }
    return false;
  };

  const handleRamalTactium = async (ramal) => {
    localStorage.setItem("ramalTactium", JSON.stringify(ramal));
    if (verifyCrmAuth(user)) {
      setCrmAuthModal(true);
    } else {
      history.push("/telaprincipal");
    }
  };

  const handleOlosFirstLogin = async () => {
    if (verifyCrmAuth(user)) {
      setCrmAuthModal(true);
    } else {
      history.push("/telaprincipal");
    }
  };

  const handleCrmAuth = () => {
    history.push("/telaprincipal");
  };

  useEffect(() => {
    if (passCode) {
      window.location.href = "/#/login";
    }

    return () => {
      if (connection) {
        connection
          .stop()
          .then(() => console.log("WebSocket SignalR foi fechado."))
          .catch((error) =>
            console.error("Erro ao fechar o WebSocket SignalR:", error)
          );
      }
    };
  }, [passCode]);

  const handleCheckUseTelefonia = () => {
    setCheckUseTelefonia(!checkUseTelefonia);
  };

  return (
    <CContainer
      fluid
      className="d-flex align-items-center justify-content-center vh-100"
    >
      <CRow className="w-100 d-flex align-items-center">
        <CCol xs="12" md="6">
          <CCard className="h-100 mb-0">
            <div style={{ alignItems: "center" }}>
              <img
                src="/images/tela_login.png"
                alt="Tela Única Login"
                width="100%"
              />
            </div>
          </CCard>
        </CCol>
        {!firstAccess && (
          <CCol xs="12" md="6">
            <CCard>
              <CCardBody style={{ textAlign: "center" }}>
                <h3>Seja bem vindo!</h3>
                <CForm>
                  <CFormGroup>
                    <CLabel color="gray" htmlFor="email">
                      Faça login pelo seu usuário
                    </CLabel>
                    <CInput
                      type="text"
                      id="email"
                      placeholder="Insira aqui seu usuário"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                  </CFormGroup>
                  <CFormGroup>
                    <CInput
                      type="password"
                      id="password"
                      placeholder="Insira aqui sua senha"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      autoComplete="off"
                      required
                    />
                  </CFormGroup>
                  <div className="form-check d-flex justify-content-start mb-2">
                    <input
                      checked={checkUseTelefonia}
                      type="checkbox"
                      className="form-check-input"
                      role="switch"
                      id="usarTelefonia"
                      onChange={handleCheckUseTelefonia}
                    />
                    <label className="form-check-label" htmlFor="usarTelefonia">
                      Acessar Telefonia
                    </label>
                    {!checkUseTelefonia && (
                      <span className="text-danger  px-4">
                        ATENÇÃO: SEMPRE ABRIR "SEM TELEFONIA ATIVA" EM ABA
                        ANÔNIMA DO NAVEGADOR!
                      </span>
                    )}
                  </div>

                  {isLoading ? (
                    <div>
                      <LoadingComponent />
                    </div>
                  ) : (
                    <CButton
                      type="submit"
                      color="primary"
                      block
                      onClick={handleClick}
                    >
                      Acessar minha conta
                    </CButton>
                  )}
                </CForm>
              </CCardBody>
            </CCard>
            <>
              <h3>Atenção:</h3>
              <p class="lead">
                Fique atento as normas de segurança para realizar login no
                sistema:
              </p>
              <p>
                <strong>Errou o usuário ou senha duas vezes: </strong>Bloqueio
                de APIs de Integração com Datacob.
              </p>
              <p>
                <strong>Errou o usuário ou senha cinco vezes: </strong>Bloqueio
                total de acesso.
              </p>
            </>
          </CCol>
        )}
        {firstAccess && (
          <CCol xs="12" md="6">
            <CCard>
              <CCardBody style={{ textAlign: "center" }}>
                <h3>Seja bem vindo!</h3>
                <CForm>
                  <CFormGroup>
                    <CLabel color="gray" htmlFor="email">
                      Atualize sua senha para acessar o sistema
                    </CLabel>
                    <CInput
                      type="password"
                      id="password"
                      placeholder="Insira aqui sua senha"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                      autoComplete="off"
                      required
                      style={{
                        ...(newPassword !== "" &&
                        newPassword !== null &&
                        newPassword !== undefined
                          ? {
                              borderColor:
                                !senhaValidacao.hasUpperCase ||
                                !senhaValidacao.hasLowerCase ||
                                !senhaValidacao.hasNumber ||
                                !senhaValidacao.hasSpecialChar ||
                                !senhaValidacao.hasLength ||
                                !senhaValidacao.matchPassword
                                  ? "red"
                                  : "",
                            }
                          : {}),
                      }}
                    />
                  </CFormGroup>
                  <CFormGroup>
                    <CInput
                      type="password"
                      id="confirmPassword"
                      placeholder="Confirme sua senha"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      autoComplete="off"
                      required
                      style={{
                        ...(newPassword !== "" &&
                        newPassword !== null &&
                        newPassword !== undefined
                          ? {
                              borderColor:
                                !senhaValidacao.hasUpperCase ||
                                !senhaValidacao.hasLowerCase ||
                                !senhaValidacao.hasNumber ||
                                !senhaValidacao.hasSpecialChar ||
                                !senhaValidacao.hasLength ||
                                !senhaValidacao.matchPassword
                                  ? "red"
                                  : "",
                            }
                          : {}),
                      }}
                    />
                  </CFormGroup>

                  {isLoading ? (
                    <div>
                      <LoadingComponent />
                    </div>
                  ) : (
                    <CButton
                      type="submit"
                      color="primary"
                      block
                      onClick={handleFirstAcessClick}
                    >
                      Atualizar e acessar minha conta
                    </CButton>
                  )}
                </CForm>
              </CCardBody>
            </CCard>
            {newPassword !== "" &&
              newPassword !== null &&
              newPassword !== undefined && (
                <small
                  className="form-text"
                  style={{
                    color: "red",
                    fontSize: "12px",
                    marginTop: "5px",
                  }}
                >
                  {!senhaValidacao.hasUpperCase && (
                    <div>Obrigatório uma letra maiúscula</div>
                  )}
                  {!senhaValidacao.hasLowerCase && (
                    <div>Obrigatório uma letra minúscula</div>
                  )}
                  {!senhaValidacao.hasNumber && (
                    <div>Obrigatório um número</div>
                  )}
                  {!senhaValidacao.hasSpecialChar && (
                    <div>Obrigatório um caractere especial</div>
                  )}
                  {!senhaValidacao.hasLength && (
                    <div>Obrigatório no mínimo 8 caracteres</div>
                  )}
                  {!senhaValidacao.matchPassword && (
                    <div>As senhas não coincidem</div>
                  )}
                </small>
              )}
          </CCol>
        )}
        {showModalTactium && (
          <RamalModal
            isOpen={showModalTactium}
            onClose={() => setShowModalTactium(false)}
            onSubmit={handleRamalTactium}
          />
        )}
        {showModalOlosFirst && (
          <OlosFirstLoginModal
            isOpen={showModalOlosFirst}
            onClose={() => setShowModalOlosFirstLogin(false)}
            onSubmit={handleOlosFirstLogin}
          />
        )}
        {crmAuthModal && (
          <CrmAuthModal
            onClose={() => {
              setCrmAuthModal(false);
              handleCrmAuth();
            }}
            chosenCrm={chosenCrm}
          />
        )}
      </CRow>
    </CContainer>
  );
};

export default Login;
