import React from "react";
import { CChartBar } from "@coreui/react-chartjs";

interface GraficoPerformanceAnualProps {
  clients: any[];
}

const GraficoPerformanceAnual: React.FC<GraficoPerformanceAnualProps> = ({
  clients,
}) => {
  const hoje = new Date();
  const mesesLabels: string[] = [];
  const valoresMensais: number[] = [];
  const quantidadeContratos: number[] = [];

  for (let i = 11; i >= 0; i--) {
    const data = new Date(hoje.getFullYear(), hoje.getMonth() - i, 1);
    const label = data.toLocaleString("pt-BR", {
      month: "short",
      year: "2-digit",
    });
    mesesLabels.push(label);
    valoresMensais.push(0);
    quantidadeContratos.push(0);
  }

  clients.forEach((client) => {
    client.contracts.forEach((contract) => {
      if (contract.negotiationDate && contract.isConverted) {
        const data = new Date(contract.negotiationDate.replace(" ", "T"));
        const valor = contract.negotiationAmount;
        mesesLabels.forEach((_, idx) => {
          const mesData = new Date(
            hoje.getFullYear(),
            hoje.getMonth() - (11 - idx),
            1
          );
          if (
            data.getFullYear() === mesData.getFullYear() &&
            data.getMonth() === mesData.getMonth()
          ) {
            valoresMensais[idx] += valor;
            quantidadeContratos[idx] += 1;
          }
        });
      }
    });
  });
  const maxValor = Math.max(...valoresMensais);
  const melhorMesIndex = valoresMensais.indexOf(maxValor);
  const maxContratos = Math.max(...quantidadeContratos);
  const melhorContratoIndex = quantidadeContratos.indexOf(maxContratos);
  const coresValores = valoresMensais.map((_, idx) =>
    idx === melhorMesIndex ? "#28a745" : "#2f65f6"
  );
  const coresContratos = quantidadeContratos.map((_, idx) =>
    idx === melhorContratoIndex ? "#ff5733" : "#f6b12f"
  );
  const datasets = [
    {
      label: "Total Negociado",
      backgroundColor: coresValores,
      borderColor: coresValores,
      data: valoresMensais,
      yAxisID: "y-valor",
    },
    {
      label: "Qtd. Contratos",
      backgroundColor: coresContratos,
      borderColor: coresContratos,
      data: quantidadeContratos,
      yAxisID: "y-contratos",
    },
  ];

  const options = {
    responsive: true,
    legend: { display: true },
    tooltips: {
      callbacks: {
        label: function (tooltipItem: any, data: any) {
          const datasetLabel = data.datasets[tooltipItem.datasetIndex].label;
          const value = tooltipItem.yLabel || 0;
          return datasetLabel === "Total Negociado"
            ? `R$ ${value.toLocaleString("pt-BR", {
                minimumFractionDigits: 2,
              })}`
            : `${value} contratos`;
        },
      },
    },
    scales: {
      yAxes: [
        {
          id: "y-valor",
          position: "left",
          ticks: {
            display: false,
          },
          gridLines: {
            drawTicks: false,
            display: false,
          },
        },
        {
          id: "y-contratos",
          position: "right",
          ticks: {
            display: false,
          },
          gridLines: {
            drawTicks: false,
            drawOnChartArea: false,
            display: false,
          },
        },
      ],
      xAxes: [
        {
          gridLines: { display: true },
        },
      ],
    },
    animation: {
      onComplete: function () {
        const chart = this.chart;
        const ctx = chart.ctx;
        ctx.font = "bold 12px sans-serif";
        ctx.fillStyle = "#000";
        ctx.textAlign = "center";
        ctx.textBaseline = "bottom";

        chart.data.datasets.forEach((dataset: any, i: number) => {
          const meta = chart.getDatasetMeta(i);
          meta.data.forEach((bar: any, index: number) => {
            const value = dataset.data[index];
            if (value > 0) {
              ctx.fillText(
                dataset.label === "Total Negociado"
                  ? `R$ ${value.toLocaleString("pt-BR")}`
                  : `${value}`,
                bar._model.x,
                bar._model.y - 5
              );
            }
          });
        });
      },
    },
  };
  return (
    <CChartBar datasets={datasets} options={options} labels={mesesLabels} />
  );
};

export default GraficoPerformanceAnual;
