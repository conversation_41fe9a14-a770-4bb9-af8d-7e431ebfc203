import React, { useState } from "react";
import { CRow, <PERSON>ol, CButton, CTooltip } from "@coreui/react";
import "react-toastify/dist/ReactToastify.css";
import "react-datepicker/dist/react-datepicker.css";
import ControleUsuariosPixDetalisModal from "./controleUsuariosPixDetalisModal";

const ControleUsuariosPixTable = ({ pix }) => {
  const [selectedTicket, setSelectedTicket] = useState(null);
  const [showModalPixDetails, setShowModalPixDetails] = useState(false);

  const handleDetais = (ticket) => {
    setSelectedTicket(ticket);
    setShowModalPixDetails(true);
  };

  const handleCloseModals = () => {
    setShowModalPixDetails(false);
  };

  return (
    <CCol className={"p-4"}>
      <CRow>
        <div
          className="table-responsive"
          style={{ overflow: "auto", maxHeight: "350px" }}
        >
          <table className="table">
            <thead>
              <tr>
                <th>Nome</th>
                <th>Nr. Contrato</th>
                <th>Nr. Parcela</th>
                <th>Status</th>
                <th>Carteira</th>
                <th>Usuário</th>
                <th>Ações</th>
              </tr>
            </thead>
            <tbody>
              {pix?.length < 1 && (
                <tr>
                  <td colspan="16" className="text-center">
                    Nenhum Registro Encontrado!
                  </td>
                </tr>
              )}
              {pix?.map((item, index) => {
                return (
                  <tr key={index}>
                    <td>{item.nomeCliente}</td>
                    <td>{item.numeroContrato}</td>
                    <td>
                      {item.primeiraParcela}{" "}
                      {item?.parcelas?.includes(",") && (
                        <CTooltip content={item.parcelas}>
                          <span
                            style={{ cursor: "default" }}
                            className="badge badge-info"
                          >
                            +
                          </span>
                        </CTooltip>
                      )}
                    </td>
                    <td>{item.statusPix}</td>
                    <td>{item.descricaoCarteira}</td>
                    <td>{item.nomeUsuario}</td>
                    <td className="d-flex">
                      <CTooltip content="Mais Informações">
                        <CButton
                          color="info"
                          onClick={() => handleDetais(item)}
                          className={"mr-2"}
                        >
                          <i className="cil-plus"></i>
                        </CButton>
                      </CTooltip>
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </CRow>
      {showModalPixDetails && (
        <ControleUsuariosPixDetalisModal
          isOpen={showModalPixDetails}
          onClose={handleCloseModals}
          ticket={selectedTicket}
        />
      )}
    </CCol>
  );
};

export default ControleUsuariosPixTable;
