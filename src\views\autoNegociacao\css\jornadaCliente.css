.jornada-grid {
  display: flex;
  flex-wrap: wrap;
}

.coluna {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.coluna-titulo {
  font-size: 0.9rem;
  font-weight: 500;
  color: #6c757d;
  margin-bottom: 0.25rem;
  min-height: 48px;
  display: flex;
  align-items: center;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bloco {
  border-radius: 12px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  color: white;
  text-align: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-height: 80px;
}

.bloco:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12);
}

.bloco .titulo {
  font-weight: 600;
  font-size: 0.9rem;
  margin-bottom: 0.2rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bloco .valor {
  font-size: 0.9rem;
  font-weight: 700;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.porcentagem {
  font-size: 0.8rem;
  font-weight: 400;
}

.bloco.azul {
  background: #1677ff;
}

.bloco.laranja {
  background: #fa8c16;
}

.bloco.verde {
  background: #52c41a;
}

.bloco.vermelho {
  background: #ff4d4f;
}

.bloco.roxo {
  background: #8c8cf9;
}

.bloco.ciano {
  background: #13c2c2;
}
