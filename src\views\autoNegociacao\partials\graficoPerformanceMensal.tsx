import React from "react";
import { CChartLine } from "@coreui/react-chartjs";

interface GraficoPerformanceMensalProps {
  clients: any[];
}
const GraficoPerformanceMensal: React.FC<GraficoPerformanceMensalProps> = ({
  clients,
}) => {
  const hoje = new Date();
  const anoAtual = hoje.getFullYear();
  const mesAtual = hoje.getMonth();
  const diaHoje = hoje.getDate();
  const dataMesAnterior = new Date(anoAtual, mesAtual - 1, 1);
  const anoAnterior = dataMesAnterior.getFullYear();
  const mesAnterior = dataMesAnterior.getMonth();
  const diasMesAtual = new Date(anoAtual, mesAtual + 1, 0).getDate();
  const diasMesAnterior = new Date(anoAnterior, mesAnterior + 1, 0).getDate();
  const valoresAtual = Array(diasMesAtual).fill(0);
  const valoresAnterior = Array(diasMesAnterior).fill(0);

  clients.forEach((client) => {
    client.contracts.forEach((contract) => {
      if (contract.negotiationDate && contract.isConverted) {
        const data = new Date(contract.negotiationDate.replace(" ", "T"));
        const valor = contract.negotiationAmount;

        if (data.getFullYear() === anoAtual && data.getMonth() === mesAtual) {
          valoresAtual[data.getDate() - 1] += valor;
        }

        if (
          data.getFullYear() === anoAnterior &&
          data.getMonth() === mesAnterior
        ) {
          valoresAnterior[data.getDate() - 1] += valor;
        }
      }
    });
  });

  const acumuladoAtual = valoresAtual.reduce<number[]>(
    (valoresAcumulados, valorDoDia, indiceDia) => {
      if (indiceDia >= diaHoje) return valoresAcumulados;
      valoresAcumulados[indiceDia] =
        (valoresAcumulados[indiceDia - 1] || 0) + valorDoDia;
      return valoresAcumulados;
    },
    []
  );

  const acumuladoAnterior = valoresAnterior.reduce<number[]>(
    (valoresAcumulados, valorDoDia, indiceDia) => {
      valoresAcumulados[indiceDia] =
        (valoresAcumulados[indiceDia - 1] || 0) + valorDoDia;
      return valoresAcumulados;
    },
    []
  );

  const valoresMensais: { [key: string]: number[] } = {};
  clients.forEach((client) => {
    client.contracts.forEach((contract) => {
      if (contract.negotiationDate && contract.isConverted) {
        const data = new Date(contract.negotiationDate.replace(" ", "T"));
        const key = `${data.getFullYear()}-${data.getMonth()}`;

        if (!valoresMensais[key]) {
          const dias = new Date(
            data.getFullYear(),
            data.getMonth() + 1,
            0
          ).getDate();
          valoresMensais[key] = Array(dias).fill(0);
        }

        valoresMensais[key][data.getDate() - 1] += contract.negotiationAmount;
      }
    });
  });

  let melhorMesKey = "";
  let melhorTotal = 0;
  Object.keys(valoresMensais).forEach((key) => {
    const total = valoresMensais[key].reduce((a, b) => a + b, 0);
    if (total > melhorTotal) {
      melhorTotal = total;
      melhorMesKey = key;
    }
  });

  let acumuladoMelhorMes: number[] = [];
  if (melhorMesKey) {
    const valores = valoresMensais[melhorMesKey];
    acumuladoMelhorMes = valores.reduce<number[]>((acc, valor, idx) => {
      acc[idx] = (acc[idx - 1] || 0) + valor;
      return acc;
    }, []);
  }

  const totalAtual = acumuladoAtual[hoje.getDate() - 1] || 0;
  const totalAnterior = acumuladoAnterior[diasMesAnterior - 1] || 0;
  const diasMax = Math.max(diasMesAtual, diasMesAnterior);
  const labels = Array.from({ length: diasMax }, (_, i) => `${i + 1}`);

  const datasets = [
    {
      label: `Mês Atual: R$ ${totalAtual.toLocaleString("pt-BR", {
        minimumFractionDigits: 2,
      })}`,
      data: acumuladoAtual,
      borderColor: "#2f65f6",
      backgroundColor: "#2f65f6",
      tension: 0.3,
      fill: false,
      pointRadius: 4,
      pointHoverRadius: 5,
    },
    {
      label: `Mês Anterior: R$ ${totalAnterior.toLocaleString("pt-BR", {
        minimumFractionDigits: 2,
      })}`,
      data: acumuladoAnterior,
      borderColor: "#f6b12f",
      backgroundColor: "#f6b12f",
      tension: 0.3,
      fill: false,
      pointRadius: 4,
      pointHoverRadius: 5,
    },
    {
      label: `Melhor Mês (12 meses): R$ ${melhorTotal.toLocaleString("pt-BR", {
        minimumFractionDigits: 2,
      })}`,
      data: acumuladoMelhorMes,
      borderColor: "#28a745",
      backgroundColor: "#28a745",
      borderDash: [5, 5],
      tension: 0.3,
      fill: false,
      pointRadius: 3,
      pointHoverRadius: 4,
    },
  ];

  const options = {
    responsive: true,
    legend: {
      position: "top" as const,
    },
    scales: {
      yAxes: [
        {
          scaleLabel: {
            display: true,
            labelString: "Valor Acumulado",
          },
          ticks: {
            beginAtZero: true,
            callback: function (value: number) {
              return `R$ ${value.toLocaleString("pt-BR")}`;
            },
          },
        },
      ],
      xAxes: [
        {
          scaleLabel: {
            display: true,
            labelString: "Dia do Mês",
          },
        },
      ],
    },
  };

  return (
    <div>
      <CChartLine datasets={datasets} options={options} labels={labels} />
    </div>
  );
};

export default GraficoPerformanceMensal;
