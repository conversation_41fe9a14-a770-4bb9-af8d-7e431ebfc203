import {
  <PERSON><PERSON>on,
  <PERSON>ard,
  <PERSON>ard<PERSON>ody,
  <PERSON>ard<PERSON>ooter,
  CCol,
  CInput,
  CLabel,
  CRow,
} from "@coreui/react";
import React, { useState, useEffect, useRef } from "react";
import Select from "react-select";
import { GET_DATA, POST_DATA } from "src/api";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import SendEmailModal from "./SendEmailModal";
import CardLoading from "src/reusable/CardLoading";
import { toast } from "react-toastify";
import TableInstallment from "../Parcial/TableInstallment";
import LoadingComponent from "src/reusable/Loading";

const BancoRodobens = ({ onClose, cleanCalculoPost }) => {
  const textData = useRef(null);
  const [dataFields, setDataFields] = useState(null);
  const [vlHo, setVlHo] = useState(null);
  const [percHo, setPercHo] = useState(null);
  const [custas, setCustas] = useState(0);
  const [valorPrincipal, setValorPrincipal] = useState(0);
  const [valorPrincipalFcDesconto, setValorPrincipalFcDesconto] = useState(0);
  const [vlNegociacao, setVlNegociacao] = useState(0);
  const [percDesconto, setPercDesconto] = useState(0);
  const [isLoading, setLoading] = useState(false);
  const [negociador, setNegociador] = useState("");
  const [vencimento, setVencimento] = useState(new Date());
  // const [selectedContrato, setSelectedContrato] = useState({
  //   label: "Todos",
  //   value: null,
  // });
  const [selectedContrato, setSelectedContrato] = useState("");
  const [email, setEmail] = useState(null);
  const [selectedPhone, setSelectedPhone] = useState(null);
  const [selectedOcorrencia, setSelectedOcorrencia] = useState(null);
  const [optionsPhone, setOptionsPhone] = useState([]);
  const [optionsOcorrencia, setOptionsOcorrencia] = useState([]);
  const [optionsEmail, setOptionsEmail] = useState([]);
  const [ativo, setAtivo] = useState("");
  const [showSendEmailModal, setShowSendEmailModal] = useState(false);
  const [nrParcelas, setNrParcelas] = useState("");
  const [showFieldsHidden, setShowFieldsHidden] = useState(false);
  const [numerosParcelasPorContrato, setNumerosParcelasPorContrato] =
    useState(null);
  const [checkedAll, setCheckedAll] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [qtdParcela, setQtdParcela] = useState(0);
  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : null;

  const userData = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const calcTotalValue = (item) => {
    return (
      item.vlOriginal +
      item.vlHoOriginal +
      item.vlJurosOriginal +
      item.vlMultaOriginal +
      item.vlComPermanenciaOriginal +
      item.vlDespesasOriginal +
      item.vlNotificacaoOriginal +
      item.vlTarifaOriginal
    );
  };
  const handleContratoChange = async (selection) => {
    setSelectedContrato(selection);
  };
  const handleValueChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (input.length > 11) return;

    setPercHo(null);
    setVlHo(value);
  };

  const handlePercChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (value > 100) value = 100;
    if (isNaN(value)) value = 0;

    setVlHo(null);
    setPercHo(value);
  };
  const handleNumberChange = (event, setFunc) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (input.length > 11 || isNaN(value)) return;

    setFunc(value);
  };

  const getCalculo = () => {
    const vlPrincipalFcDesconto =
      valorPrincipal - valorPrincipal * (percDesconto / 100);
    const valorHonorario =
      vlPrincipalFcDesconto > 0 ? vlPrincipalFcDesconto * (percHo / 100) : 0;

    setValorPrincipalFcDesconto(vlPrincipalFcDesconto);
    const valNegociacao = vlPrincipalFcDesconto + valorHonorario + custas;
    setVlNegociacao(valNegociacao);
    setVlHo(valorHonorario);

    const data = {
      negociador: negociador,
      ativo: ativo,
      email: email?.value,
      vencimento: "",
      parcelas: nrParcelas,
      valorPrincipal: valorPrincipal,
      vlHo: valorHonorario,
      percHo: percHo,
      percDesconto: percDesconto,
      custas: custas,
      vlPrincipalFcDesconto: vlPrincipalFcDesconto,
      valorNegociacao: valNegociacao,
    };
    setDataFields(data);
    setShowFieldsHidden(true);
  };
  const SELECTED_DATE = new Date();
  const COLUMNS = [
    {
      label: "",
    },
    {
      key: "numero_Contrato",
      defaultSort: "ascending",
      label: "Contrato",
      filter: true,
    },
    {
      key: "nr_Parcela",
      label: "Parcela",
      cellStyleCondicional: (item) => {
        if (item.atraso && item.atraso > 0) {
          return {
            backgroundColor: "red",
            color: "white",
            textAlign: "center",
          };
        }
        return {
          backgroundColor: "white",
          color: "black",
          textAlign: "center",
        };
      },
      formatter: (value) => String(value).padStart(3, "0"),
    },
    { key: "nr_Plano", label: "Plano" },
    {
      key: "nome_Tipo_Parcela",
      label: "Tp. Parcela",
    },
    {
      key: "dt_Vencimento",
      label: "Vencimento",
      formatter: (value) => formatDate(value),
    },
    {
      key: "vl_Saldo",
      label: "Valor Saldo",
      formatter: (value) => formatThousands(value),
    },
    {
      key: "vl_Saldo_Atualizado",
      label: "Valor Total",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "valorNegociado",
      label: "Valor Negociado",
      formatter: (value) => formatCurrency(value, false),
    },
    {
      key: "dt_Pgto",
      label: "Data Pagamento",
      defaultValue: SELECTED_DATE ? formatDate(SELECTED_DATE) : "---",
    },
    { key: "atraso", label: "Atraso", formatter: (value) => value.toString() },
    //Esses dois campos de desconto são provavelmente calculados aqui no front e carregados na tabela
    // {
    //   key: "desconto",
    //   label: "Desconto",
    //   formatter: (value) => formatCurrency(value, false),
    // },
    // {
    //   key: "percDesconto",
    //   label: "% de Desconto",
    //   formatter: (value) => formatCurrency(value, false),
    // },
  ];

  async function getTiposOcorrencia() {
    setLoading(true);
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    if (tiposOcorrencia !== null && tiposOcorrencia !== undefined) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.cod_Ocorr_Sistema + " - " + item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];
      setOptionsOcorrencia(options);
    }
    setLoading(false);
  }
  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;
  const handleSaveOccurrence = async () => {
    if (selectedPhone === null || selectedOcorrencia === null) {
      toast.warning("Selecione o telefone e a ocorrência!");
      return;
    }
    toast.info("Enviando ocorrência...");
    const payload = {
      login: userData?.username,
      id_Contrato: financiadoData?.id_Contrato,
      id_Ocorrencia_Sistema: selectedOcorrencia?.value,
      observacao: textData.current.innerText,
      telefones: [selectedPhone?.value],
      complemento: "",
      telefoneParaRetorno: selectedPhone?.value,
      ...(financiadoData?.idLinkedGroup &&
        financiadoData?.idGrupo && {
          groupId: financiadoData?.idGrupo,
          linkedGroupId: financiadoData?.idLinkedGroup,
        }),
    };
    const ocorrencia = await POST_DATA(
      "Datacob/historicoAdicionar",
      payload,
      false,
      true
    );
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
    } else {
      toast.error("Erro ao enviar ocorrência!");
    }
  };
  const handlePhoneChange = (event) => {
    setSelectedPhone(event);
  };

  const handleChangeEmail = (event) => {
    setEmail(event);
  };
  const handleCloseClick = () => {
    onClose();
  };

  const handleOcorrenciaChange = (event) => {
    setSelectedOcorrencia(event);
  };

  const asyncFunc = async () => {
    setLoading(true);
    // busca Calculo Datacob
    await cleanCalculoPost(
      [],
      0,
      0,
      new Date(vencimento),
      handleCalculoSuccess,
      emptyFunc,
      emptyFunc,
      () => {
        setLoading(false);
      }
    );
  };
  const parcelasAbertas =
    contratosAtivos === null || contratosAtivos === undefined
      ? []
      : contratosAtivos?.flatMap((item) =>
          item.parcelas.filter(
            (pItem) =>
              pItem.status === "A" &&
              !pItem.nr_Acordo /* && pItem.atraso > 0  ||
    (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
          )
        );

  const handleSelectAll = (e) => {
    tableData.map((item) => {
      if (
        item.nrContrato?.replaceAll(" ", "") ===
        selectedContrato?.replaceAll(" ", "")
      )
        item.parcelaSelecionada = e.target.checked;
      else if (selectedContrato === "")
        item.parcelaSelecionada = e.target.checked;
      return item;
    });
    setTableData(tableData);
    const vlMinimo = tableData
      .filter((x) => x.parcelaSelecionada)
      .reduce((a, b) => a + b.vlAtualizadoDescontoMax, 0);
    setValorPrincipal(vlMinimo);

    setQtdParcela(tableData.length);
    setCheckedAll(e.target.checked);
  };

  const HandleInstallmentDatacobChange = (input, item) => {
    const selec = tableData.map((x) => {
      if (x.idParcela === item.idParcela)
        x.parcelaSelecionada = input.target.checked;
      return x;
    });
    setTableData(selec);

    const parcSelec = selec.filter((x) => x.parcelaSelecionada);

    const vlMinimo = parcSelec.reduce((a, b) => a + b.vlOriginal, 0);
    setValorPrincipal(vlMinimo);

    const cust = parcSelec.reduce((a, b) => a + b.vlDespesasOriginal, 0);

    setCustas(cust);

    setCheckedAll(
      tableData
        .filter((x) =>
          selectedContrato !== null &&
          selectedContrato !== undefined &&
          selectedContrato !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              selectedContrato?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };
  const handleCalculoSuccess = (response) => {
    const neg = response.negociacaoDto[0] ?? null;

    let contratosSelecionados = neg?.parcelas;
    if (
      selectedContrato !== null &&
      selectedContrato !== undefined &&
      selectedContrato !== ""
    ) {
      contratosSelecionados = neg?.parcelas.filter(
        (x) => x.nrContrato === selectedContrato
      );
    }

    const parcelas = contratosSelecionados;
    if (parcelas === undefined || parcelas === null) return [];

    const obj = parcelas
      // .filter((item) => {
      //   const parcAb = parcelasAbertas.find(
      //     (x) => x.id_Parcela === item.idParcela
      //   );
      //   return item.atraso > 0 || parcAb?.nome_Tipo_Parcela === "DIF_PARCELAS";
      // })
      .map((item) => {
        const parcAb = parcelasAbertas.find(
          (x) => x.id_Parcela === item.idParcela
        );
        item.parcelaSelecionada = true;
        return {
          ...item,
          nome_Tipo_Parcela: parcAb?.nome_Tipo_Parcela,
          dt_Pgto: parcAb?.dt_Pgto,
        };
      });

    const honorariosNegociacao = obj
      ? obj.reduce(
          (total, item) => Math.round((total + item.vlHoNegociado) * 100) / 100,
          0
        )
      : 0;
    const custasNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlDespesasNegociado) * 100) / 100,
          0
        )
      : 0;
    const notificacaoNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlNotificacaoNegociado) * 100) / 100,
          0
        )
      : 0;
    const tarifaNegociacao = obj
      ? obj.reduce(
          (total, item) =>
            Math.round((total + item.vlTarifaNegociado) * 100) / 100,
          0
        )
      : 0;
    const iofNegociacao = 0;
    const totalNegociacao = obj
      ? obj.reduce(
          (total, item) => Math.round((total + item.vlAtualizado) * 100) / 100,
          0
        )
      : 0;

    const valorPrincipal = obj.reduce((acc, item) => acc + item.vlOriginal, 0);
    setValorPrincipal(valorPrincipal);

    const subTotalNegociacao = obj
      ? totalNegociacao -
        honorariosNegociacao -
        custasNegociacao -
        notificacaoNegociacao -
        tarifaNegociacao -
        iofNegociacao
      : 0;
    let percHo = parseFloat(
      ((honorariosNegociacao * 100) / subTotalNegociacao).toFixed(2)
    );
    if (isNaN(percHo)) percHo = 0;
    if (percHo > 100) percHo = 100;
    if (percHo < 0) percHo = 0;

    setPercHo(percHo);
    setCustas(custasNegociacao);
    setVlNegociacao(totalNegociacao);
    setTableData(obj);
  };

  const emptyFunc = () => {};

  const handleChangeSelectContract = (e) => {
    setSelectedContrato(e.target.value);

    setCheckedAll(
      tableData
        .filter((x) =>
          e.target.value !== null &&
          e.target.value !== undefined &&
          e.target.value !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              e.target.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };
  useEffect(() => {
    asyncFunc();
  }, [selectedContrato, vencimento]);

  useEffect(() => {
    setShowFieldsHidden(false);
  }, [valorPrincipal, percHo, custas, percDesconto, ativo, negociador, email]);

  useEffect(() => {
    if (userData) {
      setNegociador(userData.name);
    }
    getTiposOcorrencia();
    const optPhone = clientData?.telefones.map((item) => {
      return {
        label: item.ddd + item.fone,
        value: item.ddd + item.fone,
      };
    });
    setOptionsPhone(optPhone);
    const optEmail = clientData?.emails?.map((item) => {
      return {
        label: item.endereco_Email,
        value: item.endereco_Email,
      };
    });
    setOptionsEmail(optEmail);
  }, []);

  return (
    <div>
      <CRow className="my-2">
        <CCol className="">
          <CCard style={{ maxHeight: "300px" }}>
            {isLoading ? (
              <div className="mt-5 mb-5">
                <LoadingComponent />
              </div>
            ) : (
              <TableInstallment
                columns={COLUMNS}
                selectAll={checkedAll}
                selectedDate={SELECTED_DATE}
                selectedContract={selectedContrato}
                contratosAtivos={contratosAtivos}
                tableData={tableData}
                handleSelectAll={handleSelectAll}
                handleChangeSelectContract={handleChangeSelectContract}
                calcTotalValue={calcTotalValue}
                HandleInstallmentChange={HandleInstallmentDatacobChange}
              />
            )}
          </CCard>
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol>
          <CLabel className="text-nowrap">Valor Principal</CLabel>
          <CInput
            value={formatCurrency(valorPrincipal ?? 0, false)}
            onChange={(e) => handleNumberChange(e, setValorPrincipal)}
          />
        </CCol>
        <CCol>
          <CLabel>Desconto (%)</CLabel>
          <CInput
            type="text"
            placeholder="Desconto"
            value={formatCurrency(percDesconto ?? 0, false)}
            onChange={(e) => handleNumberChange(e, setPercDesconto)}
          />
        </CCol>
        <CCol hidden={!showFieldsHidden}>
          <CLabel>Valor H.O.</CLabel>
          <CInput disabled value={formatCurrency(vlHo ?? 0, false)} />
        </CCol>
        <CCol>
          <CLabel>Perc. H.O.</CLabel>
          <CInput
            onChange={handlePercChange}
            value={formatCurrency(percHo ?? 0, false)}
          />
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol>
          <CLabel className="text-nowrap">Custas</CLabel>
          <CInput
            onChange={(e) => handleNumberChange(e, setCustas)}
            value={formatCurrency(custas ?? 0, false)}
          />
        </CCol>
        <CCol hidden={!showFieldsHidden}>
          <CLabel className="text-nowrap">
            Valor Principal ou FC - c/ desconto
          </CLabel>
          <CInput
            value={formatCurrency(valorPrincipalFcDesconto ?? 0, false)}
            disabled
          />
        </CCol>
        <CCol hidden={!showFieldsHidden}>
          <CLabel className="text-nowrap">Valor Negociação</CLabel>
          <CInput value={formatCurrency(vlNegociacao ?? 0, false)} disabled />
        </CCol>
        {/*<CCol>
          <CLabel className="text-nowrap">Parcelas</CLabel>
          <CInput
            value={nrParcelas}
            onChange={(e) => setNrParcelas(e.target.value)}
          />
        </CCol>*/}
      </CRow>
      <CRow className="my-2">
        <CCol>
          <CLabel className="text-nowrap">Negociador</CLabel>
          <CInput
            onChange={(e) => setNegociador(e.target.value)}
            value={negociador}
          />
        </CCol>
        <CCol>
          <CLabel className="text-nowrap">Ativo</CLabel>
          <CInput value={ativo} onChange={(e) => setAtivo(e.target.value)} />
        </CCol>
        <CCol>
          <CLabel className="text-nowrap">E-mail</CLabel>
          <Select
            value={email}
            options={optionsEmail}
            onChange={handleChangeEmail}
            placeholder={"Selecione"}
          />
        </CCol>
      </CRow>
      <CRow className="my-2">
        <CCol>
          <CButton
            color="info"
            onClick={getCalculo}
            className="mb-2"
            block
            disabled={isLoading}
          >
            Calcular
          </CButton>
        </CCol>
      </CRow>
      <CCard>
        {" "}
        {isLoading ? (
          <CardLoading />
        ) : (
          <CCardBody>
            {dataFields && showFieldsHidden ? (
              <div ref={textData}>
                <div>
                  <strong>Negociador:</strong> {dataFields?.negociador}
                </div>
                <div>
                  <strong>Ativo:</strong> {dataFields.ativo}
                </div>
                <div>
                  <strong> E-mail:</strong> {dataFields.email}
                </div>
                <div>
                  <strong> Vencimento:</strong> {dataFields.vencimento}
                </div>
                <div>
                  <strong>Parcelas:</strong>{" "}
                  {tableData
                    .filter((x) => x.parcelaSelecionada)
                    ?.map((x) => x.nrParcela)
                    ?.join(", ")}
                </div>

                <div>
                  <strong> Valor Principal:</strong>{" "}
                  {formatCurrency(dataFields.valorPrincipal)}
                </div>

                <div>
                  <strong> Valor H.O:</strong> {formatCurrency(dataFields.vlHo)}
                </div>
                <div>
                  <strong> Perc. H.O:</strong>{" "}
                  {formatCurrency(dataFields.percHo, false)}
                </div>
                <div>
                  <strong> Custas:</strong> {formatCurrency(dataFields.custas)}
                </div>
                <div>
                  <strong> Desconto (%):</strong>{" "}
                  {formatCurrency(dataFields.percDesconto, false)}
                </div>

                <div>
                  <strong> Valor Principal ou FC - c/ desconto:</strong>{" "}
                  {formatCurrency(dataFields.vlPrincipalFcDesconto)}
                </div>
                <div>
                  <strong> Valor Negociação:</strong>{" "}
                  {formatCurrency(dataFields.valorNegociacao)}
                </div>
              </div>
            ) : (
              <></>
            )}
          </CCardBody>
        )}
        <CCardFooter>
          <CRow>
            <CCol md="6">
              <CLabel className={"mt-2"}>Selecione a ocorrência:</CLabel>
              <Select
                value={selectedOcorrencia}
                options={optionsOcorrencia}
                onChange={handleOcorrenciaChange}
                placeholder={"Selecione"}
              />
            </CCol>
            <CCol md="6">
              <CLabel className={"mt-2"}>Selecione o telefone:</CLabel>
              <Select
                value={selectedPhone}
                options={optionsPhone}
                onChange={handlePhoneChange}
                placeholder={"Selecione"}
              />
            </CCol>
          </CRow>

          <CRow className="mt-4 text-center">
            <CCol>
              <CButton
                className={"mr-2"}
                color="success"
                onClick={handleSaveOccurrence}
                disabled={isLoading || !showFieldsHidden}
              >
                Adicionar à ocorrência
              </CButton>
              <CButton
                className={"mr-2"}
                color="info"
                onClick={() => setShowSendEmailModal(true)}
                disabled={isLoading || !showFieldsHidden}
              >
                Enviar E-mail
              </CButton>

              <CButton
                color="secondary"
                className="mr-2"
                onClick={handleCloseClick}
                disabled={isLoading}
              >
                Fechar
              </CButton>
            </CCol>
          </CRow>
        </CCardFooter>
      </CCard>
      {showSendEmailModal && (
        <SendEmailModal
          show={showSendEmailModal}
          handleClose={() => setShowSendEmailModal(false)}
          msg={textData.current.innerText}
          em={email?.value}
        />
      )}
    </div>
  );
};

export default BancoRodobens;
