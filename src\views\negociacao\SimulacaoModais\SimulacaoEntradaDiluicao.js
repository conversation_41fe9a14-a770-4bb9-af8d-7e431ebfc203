import React, { useEffect, useRef, useState } from "react";
import {
  CButton,
  CCard,
  CCardBody,
  CRow,
  CCol,
  CLabel,
  CInput,
} from "@coreui/react";
import { GET_DATA, POST_DATA } from "src/api";
import {
  formatCurrency,
  formatDate,
  formatThousands,
} from "src/reusable/helpers";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import CardLoading from "src/reusable/CardLoading";
import Select from "react-select";
import SendEmailModal from "./SendEmailModal";
import TableInstallment from "../Parcial/TableInstallment";
import LoadingComponent from "src/reusable/Loading";
import ReactDatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import { useAuth } from "src/auth/AuthContext";
import { postApi } from "src/reusable/functions";

const SELECTED_DATE = new Date();

const COLUMNS = [
  {
    label: "",
  },
  {
    key: "numero_Contrato",
    defaultSort: "ascending",
    label: "Contrato",
    filter: true,
  },
  {
    key: "nr_Parcela",
    label: "Parcela",
    cellStyleCondicional: (item) => {
      if (item.atraso && item.atraso > 0) {
        return {
          backgroundColor: "red",
          color: "white",
          textAlign: "center",
        };
      }
      return {
        backgroundColor: "white",
        color: "black",
        textAlign: "center",
      };
    },
    formatter: (value) => String(value).padStart(3, "0"),
  },
  { key: "nr_Plano", label: "Plano" },
  {
    key: "nome_Tipo_Parcela",
    label: "Tp. Parcela",
  },
  {
    key: "dt_Vencimento",
    label: "Vencimento",
    formatter: (value) => formatDate(value),
  },
  {
    key: "vl_Saldo",
    label: "Valor Saldo",
    formatter: (value) => formatThousands(value),
  },
  {
    key: "vl_Saldo_Atualizado",
    label: "Valor Total",
    formatter: (value) => formatCurrency(value, false),
  },
  {
    key: "valorNegociado",
    label: "Valor Negociado",
    formatter: (value) => formatCurrency(value, false),
  },
  {
    key: "dt_Pgto",
    label: "Data Pagamento",
    defaultValue: SELECTED_DATE ? formatDate(SELECTED_DATE) : "---",
  },
  { key: "atraso", label: "Atraso", formatter: (value) => value.toString() },
  //Esses dois campos de desconto são provavelmente calculados aqui no front e carregados na tabela
  // {
  //   key: "desconto",
  //   label: "Desconto",
  //   formatter: (value) => formatCurrency(value, false),
  // },
  // {
  //   key: "percDesconto",
  //   label: "% de Desconto",
  //   formatter: (value) => formatCurrency(value, false),
  // },
];

const SimulacaoEntradaDiluicao = ({ cleanCalculoPost }) => {
  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const contratos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : null;

  const parcelasAbertas =
    contratos === null || contratos === undefined
      ? []
      : contratos?.flatMap((item) =>
          item.parcelas.filter(
            (pItem) =>
              pItem.status === "A" &&
              !pItem.nr_Acordo /* && pItem.atraso > 0  ||
        (pItem.status === "A" && !pItem.nr_Acordo && pItem.atraso === 0 && pItem.nome_Tipo_Parcela === "DIF_PARCELAS")*/
          )
        );

  const { checkPermission } = useAuth();
  const permissaoAcordo = {
    modulo: "Acordos",
    submodulo: "Criar Acordos",
  };

  const [data, setData] = useState([]);
  const [isLoading, setLoading] = useState(false);
  const [isLoadingDatacob, setLoadingDatacob] = useState(true);
  const [juros, setJuros] = useState(0);
  const [multa, setMulta] = useState(0);
  const [custas, setCustas] = useState(0);
  const [honorario, setHonorario] = useState(0);
  const [totalEntrada, setTotalEntrada] = useState(0);
  const [valorPerc, setValorPerc] = useState(0);
  const [valorDiluir, setValorDiluir] = useState(0);
  const [valorParcelaAtualizada, setValorParcelaAtualizada] = useState(0);
  const [valorParcela, setValorParcela] = useState(0);
  const [qtdParcela, setQtdParcela] = useState(0);
  const [plano, setPlano] = useState(0);
  const [defesa, setDefesa] = useState(null);
  const [valorEntrada, setValorEntrada] = useState(0);
  const [dataPagamentoEntrada, setDataPagamentoEntrada] = useState(
    new Date().toJSON()
  );
  const [vctEntrada, setVctEntrada] = useState(new Date().toJSON());
  const [vctOriginal, setVctOriginal] = useState(new Date().toJSON());
  const [selectedOcorrencia, setSelectedOcorrencia] = useState(null);
  const [optionsOcorrencia, setOptionsOcorrencia] = useState([]);
  const [selectedPhone, setSelectedPhone] = useState(null);
  const [optionsPhone, setOptionsPhone] = useState([]);
  const [optionsEmail, setOptionsEmail] = useState([]);
  const [showSendEmailModal, setShowSendEmailModal] = useState(false);
  const [email, setEmail] = useState(null);
  const textData = useRef(null);

  const [checkedAll, setCheckedAll] = useState(false);

  const [selectedContract, setSelectedContract] = useState("");
  const [tableData, setTableData] = useState([]);

  const [valorMinimo, setValorMinimo] = useState(0);

  async function getCalculo() {
    const pay = [];

    const vlSoma = valorMinimo + juros + multa;
    const vlPerc = vlSoma * 0.3;
    const honor = vlPerc * 0.1;
    const vlTotalEntrada = vlPerc + honor + custas;
    const vlDiluir = vlSoma - vlPerc;

    setHonorario(honor);
    setValorDiluir(vlDiluir);
    setTotalEntrada(vlTotalEntrada);
    setValorPerc(vlPerc);

    pay.push({});
    setData(pay);

    const payload = {
      contractId: financiadoData?.id_Agrupamento,
      openInstallmentsValue: valorMinimo,
      interestRate: juros,
      fine: multa,
      costs: custas,
      perc: 30,
      fees: honorario,
      entryTotal: totalEntrada,
      valueToDilute: valorDiluir,
      installmentsValueUpdated: valorParcelaAtualizada,
      installmentsQuantity: qtdParcela,
      installmentsValue: valorParcela,
      entryDueDate: vctEntrada,
      originalDueDate: vctOriginal,
      plan: plano,
      negotiatedInstallments: tableData
        .filter((x) => x.parcelaSelecionada)
        ?.map((x) => x.nrParcela)
        ?.join(", "),
      entryValue: valorEntrada,
      entryPaymentDate: dataPagamentoEntrada,
      defense: defesa,
    };

    try {
      const response = await postApi(payload, "postAddtionEntrySimulation");
    } catch (error) {
      console.error(error);
    }
  }

  const buildText = (label, value = 0) => {
    if (
      label === "Saldo Vencido" ||
      label === "Parcela/Vincendo" ||
      label === "Valor H.O." ||
      label === "Custas" ||
      label === "Saldo à Vencer" ||
      label === "Saldo Total" ||
      label === "Valor H.O. à Vencer"
    ) {
      return `${label}: R$ ${formatThousands(value)}`;
    }
    if (label === "H.O. à Vencer") {
      return `${label}: ${value.toFixed(2)}%`;
    } else {
      return `${label}: ${value}`;
    }
  };

  const fieldMappings = [
    { label: "Contrato", field: "contrato" },
    { label: "Parcelas Vencidas DataCob", field: "parcVencidasDc" },
    { label: "Parcelas Vencidas NewCon", field: "parcVencidasNc" },
    { label: "Dif. Parcela", field: "parcDiff" },
    {
      label: "Custas",
      field: "custas",
    },
    { label: "Saldo Vencido", field: "vlVencido" },
    { label: "Parcelas à Vencer DataCob", field: "parcVincendoDc" },
    { label: "Parcelas à Vencer NewCon", field: "parcVincendoNc" },
    { label: "Qtde à Vencer", field: "qtdVincendo" },
    { label: "Parcela/Vincendo", field: "vlParcVincendo" },
    { label: "Valor H.O. à Vencer", field: "vlHO" },
    { label: "H.O. à Vencer", field: "percHO" },
    { label: "Saldo à Vencer", field: "vlVincendo" },
    { label: "Saldo Total", field: "vlTotal" },
  ];

  const renderTextLines = (item = null) => {
    if (item !== null) {
      return fieldMappings.map((mapping) =>
        buildText(mapping.label, item?.[mapping.field])
      );
    }
    return fieldMappings.map((mapping) =>
      buildText(mapping.label, data[0]?.[mapping.field])
    );
  };

  const handleSelectAll = (e) => {
    tableData.map((item) => {
      if (
        item.nrContrato?.replaceAll(" ", "") ===
        selectedContract?.replaceAll(" ", "")
      )
        item.parcelaSelecionada = e.target.checked;
      else if (selectedContract === "")
        item.parcelaSelecionada = e.target.checked;
      return item;
    });
    setTableData(tableData);

    const vlMinimo = tableData
      .filter((x) => x.parcelaSelecionada)
      .reduce((a, b) => a + b.vlAtualizadoDescontoMax, 0);
    setValorMinimo(vlMinimo);
    setQtdParcela(tableData.length);
    setCheckedAll(e.target.checked);
  };

  const handleNumberChange = (event, setFunc) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (input.length > 11 || isNaN(value)) return;

    setFunc(value);
  };

  async function getTiposOcorrencia() {
    setLoading(true);
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    if (tiposOcorrencia !== null && tiposOcorrencia !== undefined) {
      const options = [
        ...tiposOcorrencia.map((item) => {
          return {
            label: item.cod_Ocorr_Sistema + " - " + item.descricao,
            value: item.id_Ocorrencia_Sistema,
            cod_Ocorr_Sistema: item.cod_Ocorr_Sistema,
          };
        }),
      ];
      setOptionsOcorrencia(options);
    }
    setLoading(false);
  }

  const handleCalculoSuccess = (response) => {
    const neg = response.negociacaoDto[0] ?? null;
    const parcelas = neg?.parcelas;
    if (parcelas === undefined || parcelas === null) return [];
    const obj = parcelas.map((item) => {
      const parcAb = parcelasAbertas.find(
        (x) => x.id_Parcela === item.idParcela
      );
      item.parcelaSelecionada = false;
      item.nome_Tipo_Parcela = parcAb?.nome_Tipo_Parcela;
      item.dt_Pgto = parcAb?.dt_Pgto;
      return item;
    });

    setTableData(obj);
  };

  const handleSaveOccurrence = async () => {
    if (selectedPhone === null || selectedOcorrencia === null) {
      toast.warning("Selecione o telefone e a ocorrência!");
      return;
    }
    toast.info("Enviando ocorrência...");
    const payload = {
      login: user?.username,
      id_Contrato: financiadoData?.id_Contrato,
      id_Ocorrencia_Sistema: selectedOcorrencia?.value,
      observacao: textData.current.innerText,
      telefones: [selectedPhone?.value],
      complemento: "",
      telefoneParaRetorno: selectedPhone?.value,
      ...(financiadoData?.idLinkedGroup &&
        financiadoData?.idGrupo && {
          groupId: financiadoData?.idGrupo,
          linkedGroupId: financiadoData?.idLinkedGroup,
        }),
    };
    const ocorrencia = await POST_DATA(
      "Datacob/historicoAdicionar",
      payload,
      false,
      true
    );
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
    } else {
      toast.error("Erro ao enviar ocorrência!");
    }
  };

  const handleChangeSelectContract = (e) => {
    setSelectedContract(e.target.value);

    setCheckedAll(
      tableData
        .filter((x) =>
          e.target.value !== null &&
          e.target.value !== undefined &&
          e.target.value !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              e.target.value?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const HandleInstallmentDatacobChange = (input, item) => {
    const selec = tableData.map((x) => {
      if (x.idParcela === item.idParcela)
        x.parcelaSelecionada = input.target.checked;
      return x;
    });
    setTableData(selec);

    const parcSelec = selec.filter((x) => x.parcelaSelecionada);
    const vlMinimo = parcSelec.reduce((a, b) => a + b.vlOriginal, 0);
    setValorMinimo(vlMinimo);

    const jur = parcSelec.reduce((a, b) => a + b.vlJurosOriginal, 0);
    setJuros(jur);

    const mul = parcSelec.reduce((a, b) => a + b.vlMultaOriginal, 0);
    setMulta(mul);

    const cust = parcSelec.reduce((a, b) => a + b.vlDespesasOriginal, 0);
    setCustas(cust);

    setPlano(parcSelec[0]?.nrPlano);

    setCheckedAll(
      tableData
        .filter((x) =>
          selectedContract !== null &&
          selectedContract !== undefined &&
          selectedContract !== ""
            ? x.nrContrato?.replaceAll(" ", "") ===
              selectedContract?.replaceAll(" ", "")
            : true
        )
        .every((x) => x.parcelaSelecionada)
    );
  };

  const calcTotalValue = (item) => {
    return (
      item.vlOriginal +
      item.vlHoOriginal +
      item.vlJurosOriginal +
      item.vlMultaOriginal +
      item.vlComPermanenciaOriginal +
      item.vlDespesasOriginal +
      item.vlNotificacaoOriginal +
      item.vlTarifaOriginal
    );
  };

  const emptyFunc = () => {};

  useEffect(() => {
    const asyncFunc = async () => {
      // busca Calculo Datacob
      cleanCalculoPost(
        [],
        0,
        0,
        new Date(),
        handleCalculoSuccess,
        emptyFunc,
        emptyFunc,
        () => {
          setLoadingDatacob(false);
        }
      );
    };

    getTiposOcorrencia();
    const optPhone = clientData?.telefones.map((item) => {
      return {
        label: item.ddd + item.fone,
        value: item.ddd + item.fone,
      };
    });
    setOptionsPhone(optPhone);
    const optEmail = clientData?.emails?.map((item) => {
      return {
        label: item.endereco_Email,
        value: item.endereco_Email,
      };
    });
    setOptionsEmail(optEmail);
    asyncFunc();
  }, []);

  return (
    <div>
      <CRow>
        <CCol md="8">
          {/* //? Tabs Datacob e NewCon */}
          <div className="container-fluid px-0">
            <CCard style={{ maxHeight: "300px" }}>
              {isLoadingDatacob ? (
                <div className="mt-5 mb-5">
                  <LoadingComponent />
                </div>
              ) : (
                <TableInstallment
                  columns={COLUMNS}
                  selectAll={checkedAll}
                  selectedDate={SELECTED_DATE}
                  selectedContract={selectedContract}
                  contratosAtivos={contratos}
                  tableData={tableData}
                  handleSelectAll={handleSelectAll}
                  handleChangeSelectContract={handleChangeSelectContract}
                  calcTotalValue={calcTotalValue}
                  HandleInstallmentChange={HandleInstallmentDatacobChange}
                />
              )}
            </CCard>
          </div>
          {/* //! Tabs Datacob e NewCon */}

          {/* //? Inputs de honorários e botão de calculo =============================================================================== */}
          <CRow className="mb-2 mt-2">
            <CCol>
              <CLabel clasName="text-nowrap">Valor Parcelas em Aberto</CLabel>
              <CInput
                disabled
                value={formatCurrency(valorMinimo ?? 0, false)}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Juros</CLabel>
              <CInput
                onChange={(e) => handleNumberChange(e, setJuros)}
                value={formatCurrency(juros ?? 0, false)}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Multa</CLabel>
              <CInput
                onChange={(e) => handleNumberChange(e, setMulta)}
                value={formatCurrency(multa ?? 0, false)}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Custas</CLabel>
              <CInput
                onChange={(e) => handleNumberChange(e, setCustas)}
                value={formatCurrency(custas ?? 0, false)}
              />
            </CCol>
          </CRow>
          <CRow className="mb-2 mt-2">
            <CCol>
              <CLabel clasName="text-nowrap">Honorários</CLabel>
              <CInput disabled value={formatCurrency(honorario ?? 0, false)} />
            </CCol>
            {/* <CCol>
              <CLabel clasName="text-nowrap">Total Entrada</CLabel>
              <CInput
                onChange={(e) => setAtivo(e.target.value)}
                value={ativo}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Valor P/ Diluir</CLabel>
              <CInput
                onChange={(e) => setStatusProjuris(e.target.value)}
                value={statusProjuris}
              />
            </CCol> */}
            <CCol>
              <CLabel clasName="text-nowrap">Valor Parcela Atualizada</CLabel>
              <CInput
                onChange={(e) =>
                  handleNumberChange(e, setValorParcelaAtualizada)
                }
                value={formatCurrency(valorParcelaAtualizada ?? 0, false)}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Qtd. Parcelas de Acordo</CLabel>
              <CInput
                type="number"
                value={qtdParcela}
                onChange={(e) => setQtdParcela(e.target.value)}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Valor da Parcela</CLabel>
              <CInput
                onChange={(e) => handleNumberChange(e, setValorParcela)}
                value={formatCurrency(valorParcela ?? 0, false)}
              />
            </CCol>
          </CRow>

          <CRow className="mb-2 mt-2">
            <CCol>
              <CLabel clasName="text-nowrap">Venc. Entrada</CLabel>
              <ReactDatePicker
                selected={new Date(vctEntrada)}
                onChange={(e) => setVctEntrada(e.toJSON())}
                className="form-control"
                dateFormat="dd/MM/yyyy"
                onKeyDown={(e) => e.preventDefault()}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Venc. Original</CLabel>
              <ReactDatePicker
                selected={new Date(vctOriginal)}
                onChange={(e) => setVctOriginal(e.toJSON())}
                className="form-control"
                dateFormat="dd/MM/yyyy"
                onKeyDown={(e) => e.preventDefault()}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Plano</CLabel>
              <CInput
                onChange={(e) => setPlano(e.target.value)}
                type="number"
                value={plano}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">Valor Entrada</CLabel>
              <CInput
                onChange={(e) => handleNumberChange(e, setValorEntrada)}
                value={formatCurrency(valorEntrada, false)}
              />
            </CCol>
          </CRow>

          <CRow className="mb-2 mt-2">
            <CCol>
              <CLabel clasName="text-nowrap">Defesa</CLabel>
              <CInput
                onChange={(e) => setDefesa(e.target.value)}
                value={defesa}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">
                Data de Pagamento de Entrada
              </CLabel>
              <ReactDatePicker
                selected={new Date(dataPagamentoEntrada)}
                onChange={(e) => setDataPagamentoEntrada(e.toJSON())}
                className="form-control"
                dateFormat="dd/MM/yyyy"
                onKeyDown={(e) => e.preventDefault()}
              />
            </CCol>
            <CCol>
              <CLabel clasName="text-nowrap">E-mail</CLabel>
              {/* <CInput
                onChange={(e) => setEmail(e.target.value)}
                value={email}
              /> */}
              <Select
                value={email}
                options={optionsEmail}
                onChange={(e) => setEmail(e)}
                placeholder={"Selecione"}
              />
            </CCol>
          </CRow>
          <CButton
            color="info"
            onClick={getCalculo}
            className="mb-2"
            block
            disabled={isLoading}
          >
            Calcular
          </CButton>
          {/* //! Inputs de honorários e botão de calculo =============================================================================== */}
        </CCol>
        <CCol md="4">
          {/* <CCard className={"mb-2"}>
            <CCardBody>
              <strong>Valor mínimo: </strong>
              {formatCurrency(valorMinimo, false)}
            </CCardBody>
          </CCard> */}
          <CCard>
            {" "}
            {isLoading ? (
              <CardLoading />
            ) : (
              <>
                <CCardBody>
                  <div
                    className="d-flex flex-column"
                    style={{ maxHeight: "439px", overflowY: "auto" }}
                    ref={textData}
                  >
                    <div>
                      <div>
                        <strong>Parcelas Negociadas:</strong>{" "}
                        {tableData
                          .filter((x) => x.parcelaSelecionada)
                          ?.map((x) => x.nrParcela)
                          ?.join(", ")}
                      </div>
                      <div>
                        <strong>Valor de Entrada:</strong>{" "}
                        {formatCurrency(valorEntrada, false)}
                      </div>
                      <div>
                        <strong>Data de Pagamento da Entrada:</strong>{" "}
                        {formatDate(dataPagamentoEntrada)}
                      </div>
                      <div>
                        <strong>E-mail Cliente:</strong> {email?.value}
                      </div>
                      <div>
                        <strong>Defesa:</strong> {defesa}
                      </div>
                      <div>
                        <strong>Qtd de Parcelas:</strong> {qtdParcela}
                      </div>
                      <div>
                        <strong>Vencimento da Entrada:</strong>{" "}
                        {formatDate(vctEntrada)}
                      </div>
                      <div>
                        <strong>Vencimento da Original:</strong>{" "}
                        {formatDate(vctOriginal)}
                      </div>
                      <div>
                        <strong>Plano:</strong> {plano}
                      </div>
                    </div>
                    <br />
                    <div>
                      <div>
                        <strong>Valor Parcelas:</strong>{" "}
                        {formatCurrency(valorMinimo ?? 0, false)}
                      </div>
                      <div>
                        <strong>Juros:</strong>{" "}
                        {formatCurrency(juros ?? 0, false)}
                      </div>
                      <div>
                        <strong>Multa:</strong>{" "}
                        {formatCurrency(multa ?? 0, false)}
                      </div>
                      <div>
                        <strong>Custas:</strong>{" "}
                        {formatCurrency(custas ?? 0, false)}
                      </div>
                      <div>
                        <strong>Percentual: 30%</strong>
                      </div>
                      <div>
                        <strong>Valor Percentual:</strong>{" "}
                        {formatCurrency(valorPerc ?? 0, false)}
                      </div>
                      <div>
                        <strong>Honorário:</strong>{" "}
                        {formatCurrency(honorario ?? 0, false)}
                      </div>
                      <div>
                        <strong>Total Entrada:</strong>{" "}
                        {formatCurrency(totalEntrada, false)}
                      </div>
                      <div>
                        <strong>Valor para Diluir:</strong>{" "}
                        {formatCurrency(valorDiluir, false)}
                      </div>
                      <div>
                        <strong>Valor Parcela Atualizada:</strong>{" "}
                        {formatCurrency(
                          (qtdParcela > 0 ? valorDiluir / qtdParcela : 0) +
                            valorParcela,
                          false
                        )}
                      </div>
                    </div>
                    {/* <br />
                    <div>
                      <strong>Detalhes Contratos</strong>
                    </div>
                    <br /> */}
                    {/* {data.map((item, index) => (
                      <>
                        <div key={index}>
                          {renderTextLines(item).map((line, index) => (
                            <div key={index}>{line}</div>
                          ))}
                        </div>
                        <br />
                      </>
                    ))} */}
                  </div>
                </CCardBody>
              </>
            )}
          </CCard>
        </CCol>
      </CRow>

      <CRow>
        <CCol md="6">
          <CLabel className={"mt-2"}>Selecione a ocorrência:</CLabel>
          <Select
            value={selectedOcorrencia}
            options={optionsOcorrencia}
            onChange={(e) => setSelectedOcorrencia(e)}
            placeholder={"Selecione"}
          />
        </CCol>
        <CCol md="6">
          <CLabel className={"mt-2"}>Selecione o telefone:</CLabel>
          <Select
            value={selectedPhone}
            options={optionsPhone}
            onChange={(e) => setSelectedPhone(e)}
            placeholder={"Selecione"}
          />
        </CCol>
      </CRow>
      <CRow className="mt-4 text-center">
        <CCol>
          <CButton
            className={"mr-2"}
            color="success"
            onClick={handleSaveOccurrence}
            disabled={isLoading}
          >
            Adicionar à ocorrência
          </CButton>
          <CButton
            className={"mr-2"}
            color="info"
            onClick={() => setShowSendEmailModal(true)}
            disabled={isLoading}
          >
            Enviar E-mail
          </CButton>
        </CCol>
      </CRow>

      {showSendEmailModal && (
        <SendEmailModal
          show={showSendEmailModal}
          handleClose={() => setShowSendEmailModal(false)}
          msg={textData.current.innerText}
          em={email?.value}
        />
      )}
    </div>
  );
};

export default SimulacaoEntradaDiluicao;
