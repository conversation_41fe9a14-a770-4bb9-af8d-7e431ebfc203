import React, { useEffect, useState } from "react";
import { CCardBody } from "@coreui/react";
import { GET_DATA } from "src/api";
import { useMyContext } from "src/reusable/DataContext";
import { toast } from "react-toastify";
import TableBoletos from "../CardFinanceiro/Parcial/TableBoletos";
import DetalhesBoletoModal from "src/views/acordos/DetalhesBoletoModal";
import { getApi } from "src/reusable/functions";
import LoadingComponent from "src/reusable/Loading";

const Boletos = ({ selected }) => {
  const { data } = useMyContext();

  const [loading, setLoading] = useState(false);
  const [downBol, setDownBol] = useState(false);
  const [detalhesBoleto, setDetalhesBoleto] = useState(null);
  const [showModalDetalhesBoleto, setShowModalDetalhesBoleto] = useState(false);
  const [tableBoletos, setTableBoletos] = useState([]);
  const financiadoData = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const handleDetalhesBoleto = async (item) => {
    const data = {
      IdNegociacao: item.id_Negociacao,
      numeroContrato: financiadoData?.numero_Contrato ?? item?.numero_Contrato,
      ...(financiadoData.idLinkedGroup &&
        financiadoData.idGrupo && {
          groupId: financiadoData.idGrupo,
          linkedGroupId: financiadoData.idLinkedGroup,
        }),
    };
    const detBoletos = await GET_DATA(
      "Datacob/Negociacoes/Parcelas/Boleto",
      data
    );
    setDetalhesBoleto(detBoletos[0]);
    setShowModalDetalhesBoleto(true);
  };

  const handleVisualizarBoleto = async (item) => {
    const data = { IdBoleto: item.id_Boleto };
    const boleto = await GET_DATA("Datacob/DownloadBoleto", data);

    // Defina um título para a nova janela
    const windowTitle = "Visualização de PDF";

    // Crie uma nova janela com título
    const newWindow = window.open(
      "",
      "_blank",
      `width=800,height=600,toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,title=${windowTitle}`
    );

    // Escreva o conteúdo do PDF na nova janela
    newWindow.document.open();
    newWindow.document.write(
      '<embed width="100%" height="100%" src="data:application/pdf;base64,' +
        boleto +
        '" type="application/pdf"/>'
    );
    newWindow.document.close();
  };

  const handleBaixarBoleto = async (item) => {
    setDownBol(true);
    try {
      const data = { IdBoleto: item.id_Boleto };
      const boleto = await GET_DATA("Datacob/DownloadBoleto", data);
      const linkSource = `data:application/pdf;base64,${boleto}`;
      const downloadLink = document.createElement("a");
      const time = new Date().toLocaleTimeString().replaceAll(":", "");
      const date = new Date().toLocaleDateString().replaceAll("/", "");
      const fileName = `${item.nr_Boleto}-${date}${time}.pdf`;
      downloadLink.href = linkSource;
      downloadLink.download = fileName;
      downloadLink.click();
    } catch (err) {
      toast.danger("Erro ao baixar boleto");
    }
    setDownBol(false);
  };

  const getData = async () => {
    setLoading(true);
    const bol = await getApi(
      {
        idAgrupamento: data?.id_Agrupamento,
        numeroContrato: data?.numero_Contrato,
        ...(financiadoData?.idLinkedGroup &&
          financiadoData?.idGrupo && {
            groupId: financiadoData?.idGrupo,
            linkedGroupId: financiadoData?.idLinkedGroup,
          }),
      },
      "getBoletosContratoListar"
    );
    if (bol?.length > 0) setTableBoletos(bol);
    else setTableBoletos([]);
    setLoading(false);
  };

  useEffect(() => {
    if (selected) getData();
  }, [selected]);

  return (
    <CCardBody>
      {loading && <LoadingComponent />}
      {!loading && (
        <>
          <TableBoletos
            tableBoletos={tableBoletos}
            downBol={downBol}
            handleBaixarBoleto={handleBaixarBoleto}
            handleDetalhesBoleto={handleDetalhesBoleto}
            handleVisualizarBoleto={handleVisualizarBoleto}
            itemPerPage={4}
          />
          {showModalDetalhesBoleto && (
            <DetalhesBoletoModal
              isOpen={showModalDetalhesBoleto}
              onClose={() => setShowModalDetalhesBoleto(false)}
              dados={detalhesBoleto}
              updateModal={() => {}}
            />
          )}
        </>
      )}
    </CCardBody>
  );
};

export default Boletos;
