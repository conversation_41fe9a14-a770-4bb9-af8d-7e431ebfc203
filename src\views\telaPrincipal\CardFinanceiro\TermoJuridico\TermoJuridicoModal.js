import React, { useCallback, useEffect, useState } from "react";
import { CModal, CModalHeader } from "@coreui/react";
import { useMyContext } from "src/reusable/DataContext";
import TermoJuridicoBodyFooter from "./TermoJuridicoBodyFooter";
import TermoJuridicoProcessosTable from "./TermoJuridicoProcessosTable";
import { getApi, getApiInline } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import TermoJuridicoBensTable from "./TermoJuridicoBensTable";

const TermoJuridicoModal = ({ isOpen, onClose, datacobData }) => {
  const handleClose = () => {
    onClose();
  };

  const { data, processos } = useMyContext();

  const [loading, setLoading] = useState(false);
  const [selectedBens, setSelectedBens] = useState(null);
  const [bensData, setBensData] = useState([]);
  const [hasBensToSelect, setHasBensToSelect] = useState(false);
  const getBensData = useCallback(async () => {
    setLoading(true);
    try {
      const response = await getApi(
        { IdContrato: data?.id_Agrupamento },
        "getbensdatacob"
      );
      if (response) {
        setBensData(response);
        setSelectedBens(response?.length > 0 ? response[0] : null);
        setHasBensToSelect((response?.length ?? 0) > 1);
      }
    } catch (error) {
      console.error(error);
      setHasBensToSelect(false);
    }
    setLoading(false);
  }, [data.id_Agrupamento]);

  useEffect(() => {
    getBensData();
  }, [getBensData]);

  const [hasProcessoToSelect, setHasProcessoToSelect] = useState(
    (processos?.length ?? 0) > 1
  );
  const [selectedProcesso, setSelectedProcesso] = useState(
    processos?.length === 1 ? processos[0] : null
  );

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      className="custom-modal modal-xxl"
      centered
    >
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Dados para solicitar Termos Jurídicos:</h5>
      </CModalHeader>
      {!loading && (
        <>
          {!hasBensToSelect && !hasProcessoToSelect && (
            <TermoJuridicoBodyFooter
              onClose={handleClose}
              datacobData={datacobData}
              selectedProcesso={selectedProcesso}
              selectedBens={selectedBens}
            />
          )}

          {!hasBensToSelect && hasProcessoToSelect && (
            <TermoJuridicoProcessosTable
              processos={processos}
              onSelect={(processo) => {
                setHasProcessoToSelect(false);
                setSelectedProcesso(processo);
              }}
            />
          )}

          {hasBensToSelect && (
            <TermoJuridicoBensTable
              bensData={bensData}
              onSelect={(bens) => {
                setHasBensToSelect(false);
                setSelectedBens(bens);
              }}
            />
          )}
        </>
      )}
      {loading && <CardLoading />}
    </CModal>
  );
};

export default TermoJuridicoModal;
