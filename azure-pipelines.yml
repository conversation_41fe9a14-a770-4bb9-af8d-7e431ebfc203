trigger:
  branches:
    include:
      - prod
      - hml
      - hml_v2
      - piloto
  # Ignora qualquer outra branch não listada

pool:
  vmImage: 'windows-latest'

variables:
  - group: Frontend_Environment_Variables  # Conecta ao grupo de variáveis

stages:
# 📌 1 INSTALAÇÃO DO NODE.JS
- stage: Install_Node
  displayName: "Install Node.js and Dependencies"
  jobs:
  - job: InstallJob
    displayName: "Install Node.js"
    steps:
    - task: NodeTool@0
      inputs:
        versionSpec: '22.x'
        checkLatest: true
      displayName: "Set Node.js Version"

# 📌 2 BUILD DO FRONT-END - PROD
- stage: BuildProd
  displayName: "Build React Application - PROD"
  dependsOn: Install_Node
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/prod')
  variables:
    REACT_APP_ENV: 'PROD'
  jobs:
  - job: BuildJob
    steps:
    - script: |
        echo 'Checking Node.js and NPM versions...'
        node -v
        npm -v
      displayName: 'Checking Node.js and NPM versions'

    - script: |
        echo 'Installing dependencies...'
        npm install
      displayName: 'Installing dependencies'

    - script: |
        echo 'Building for environment: $(REACT_APP_ENV)'
      displayName: 'Check environment variables'

    - script: |
        echo 'Running Build...'
        npm run build
      displayName: 'Build React Application'

    - task: CopyFiles@2
      displayName: 'Copy Files to: $(Build.ArtifactStagingDirectory)'
      inputs:
        Contents: 'build/**'
        TargetFolder: '$(Build.ArtifactStagingDirectory)'

    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/build'
        includeRootFolder: false

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts: drop'
      inputs:
        ArtifactName: 'www-$(Build.SourceBranchName)'

# 📌 4 BUILD DO FRONT-END - PILOTO
- stage: BuildPILOTO
  displayName: "Build React Application - PILOTO"
  dependsOn: Install_Node
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/piloto')
  variables:
    REACT_APP_ENV: 'PILOTO'
  jobs:
  - job: BuildJob
    steps:
    - script: |
        echo 'Checking Node.js and NPM versions...'
        node -v
        npm -v
      displayName: 'Checking Node.js and NPM versions'

    - script: |
        echo 'Installing dependencies...'
        npm install
      displayName: 'Installing dependencies'

    - script: |
        echo 'Building for environment: $(REACT_APP_ENV)'
      displayName: 'Check environment variables'

    - script: |
        echo 'Running Build...'
        npm run build
      displayName: 'Build React Application'

    - task: CopyFiles@2
      displayName: 'Copy Files to: $(Build.ArtifactStagingDirectory)'
      inputs:
        Contents: 'build/**'
        TargetFolder: '$(Build.ArtifactStagingDirectory)'

    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/build'
        includeRootFolder: false

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts: drop'
      inputs:
        ArtifactName: 'www-$(Build.SourceBranchName)'

# 📌 5 BUILD DO FRONT-END - HML
- stage: BuildHML
  displayName: "Build React Application - HML"
  dependsOn: Install_Node
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/hml')
  variables:
    REACT_APP_ENV: 'HML'
  jobs:
  - job: BuildJob
    steps:
    - script: |
        echo 'Checking Node.js and NPM versions...'
        node -v
        npm -v
      displayName: 'Checking Node.js and NPM versions'

    - script: |
        echo 'Installing dependencies...'
        npm install
      displayName: 'Installing dependencies'

    - script: |
        echo 'Building for environment: $(REACT_APP_ENV)'
      displayName: 'Check environment variables'

    - script: |
        echo 'Running Build...'
        npm run build
      displayName: 'Build React Application'

    - task: CopyFiles@2
      displayName: 'Copy Files to: $(Build.ArtifactStagingDirectory)'
      inputs:
        Contents: 'build/**'
        TargetFolder: '$(Build.ArtifactStagingDirectory)'

    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/build'
        includeRootFolder: false

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts: drop'
      inputs:
        ArtifactName: 'www-$(Build.SourceBranchName)'

# 📌 5 BUILD DO FRONT-END - HML_v2
- stage: BuildHMLV2
  displayName: "Build React Application - HML V2"
  dependsOn: Install_Node
  condition: eq(variables['Build.SourceBranch'], 'refs/heads/hml_v2')
  variables:
    REACT_APP_ENV: 'HML'
  jobs:
  - job: BuildJob
    steps:
    - script: |
        echo 'Checking Node.js and NPM versions...'
        node -v
        npm -v
      displayName: 'Checking Node.js and NPM versions'

    - script: |
        echo 'Installing dependencies...'
        npm install
      displayName: 'Installing dependencies'

    - script: |
        echo 'Building for environment: $(REACT_APP_ENV)'
      displayName: 'Check environment variables'

    - script: |
        echo 'Running Build...'
        npm run build
      displayName: 'Build React Application'

    - task: CopyFiles@2
      displayName: 'Copy Files to: $(Build.ArtifactStagingDirectory)'
      inputs:
        Contents: 'build/**'
        TargetFolder: '$(Build.ArtifactStagingDirectory)'

    - task: ArchiveFiles@2
      displayName: 'Archive files'
      inputs:
        rootFolderOrFile: '$(Build.ArtifactStagingDirectory)/build'
        includeRootFolder: false

    - task: PublishBuildArtifacts@1
      displayName: 'Publish artifacts: drop'
      inputs:
        ArtifactName: 'www-$(Build.SourceBranchName)'

# 📌 6 Sonar Analysis
- stage: SonarQubeAnalysis
  displayName: "SonarQube Analysis"
  dependsOn:
    - BuildProd
    - BuildHML
    - BuildPILOTO
    - BuildHMLV2
  condition: or(
    in(dependencies.BuildProd.result, 'Succeeded', 'Skipped'),
    in(dependencies.BuildHML.result, 'Succeeded', 'Skipped'),
    in(dependencies.BuildHMLV2.result, 'Succeeded', 'Skipped'),
    in(dependencies.BuildPILOTO.result, 'Succeeded', 'Skipped')
    )

  jobs:
    - job: SonarQube
      displayName: "Run SonarQube Analysis"
      steps:
        - task: SonarQubePrepare@7
          inputs:
            SonarQube: 'GubolincoSonar24.12.0.100206'
            scannerMode: 'cli'
            configMode: 'manual'
            cliProjectKey: 'GVC-Tela-Unica-Frontend'
            cliSources: '.'
          continueOnError: true
        - task: SonarQubeAnalyze@7
          inputs:
            jdkversion: 'JAVA_HOME_17_X64'
          condition: succeededOrFailed()  # Executa mesmo se o Prepare falhar
          continueOnError: true
        - task: SonarQubePublish@7
          inputs:
            pollingTimeoutSec: '300'
          condition: succeededOrFailed()  # Executa mesmo se o Prepare falhar
          continueOnError: true

        - script: echo "Ignoring SonarQube failures"
          condition: always()



