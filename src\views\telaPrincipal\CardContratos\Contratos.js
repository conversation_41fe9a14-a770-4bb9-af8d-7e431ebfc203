import React, { useState, useEffect } from "react";
import {
  <PERSON>ol,
  CRow,
  CCardBody,
  CLabel,
  CBadge,
  CButton,
  CSpinner,
} from "@coreui/react";
import { formatDocument, formatCurrency } from "src/reusable/helpers";
import { useMyContext } from "src/reusable/DataContext";
import NegociacaoResumoModal from "src/views/abrirNegociacao/ResumoNegocicacoesModal";
import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import {
  useHistory,
  useLocation,
} from "react-router-dom/cjs/react-router-dom.min";
import {
  getAcordos,
  getAcordosManuais,
  getApi,
  postApi,
} from "src/reusable/functions";
import OutrosContratos from "./OutrosModals/OutrosContratos";
import { toast } from "react-toastify";

const DadosContratos = ({
  onLoadNewcon,
  selected,
  nrFaseBusca = 0,
  showAlertJuri = false,
}) => {
  const {
    data,
    custasProjuris,
    custas,
    updateCustas,
    contratos,
    updateBoletos,
  } = useMyContext();
  const [showResumoNegociacaoModal, setShowResumoNegociacaoModal] =
    useState(false);
  const [totalUnfulfilledNegotiations, setTotalUnfulfilledNegotiations] =
    useState(0);
  const [totalUnfulfilledAgreement, setTotalUnfulfilledAgreement] = useState(0);
  const [totalPendingPaymentAgreement, setTotalPendingPaymentAgreement] =
    useState(0);
  const [contratoLongoSafra, setContratoLongoSafra] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const [totalApprovedNegRpa, setTotalApprovedNegRpa] = useState(0);
  const [totalNotApprovedNegRpa, setTotalNotApprovedNegRpa] = useState(0);
  const [totalAwaitingNegRpa, setTotalAwaitingNegRpa] = useState(0);
  const [totalTicketNegRpa, setTotalTicketNegRpa] = useState(0);
  const [isNegRpaLoading, setNegRpaIsLoading] = useState(false);

  const [showAlertOtherContract, setShowAlertOtherContract] = useState(false);
  const [otherContract, setOtherContract] = useState([]);
  const [showModalOtherContract, setShowModalOtherContract] = useState(false);

  const [isLoadingCyber, setIsLoadingCyber] = useState(true);
  const [hasCyber, setHasCyber] = useState(false);
  const [loadDataNewcon, setLoadDataNewcon] = useState(false);

  const history = useHistory();
  const location = useLocation();
  const isTelaprincipal = location.pathname === "/telaprincipal";

  const searchStatus = async () => {
    setNegRpaIsLoading(true);
    const response = await GET_DATA(
      getURI("getSaveNegListByGrouping"),
      null,
      true,
      true,
      data.id_Agrupamento
    );
    if (response.length > 0) {
      setTotalApprovedNegRpa(
        response.filter((x) => x.statusNegotiation.indexOf("Aprovado") > -1)
          .length
      );
      setTotalNotApprovedNegRpa(
        response?.filter(
          (x) =>
            x.statusNegotiation === "Falha" ||
            x.statusNegotiation === "Reprovado"
        )?.length
      );
      setTotalAwaitingNegRpa(
        response?.filter((x) => x.statusNegotiation === "Pendente")?.length
      );
      setTotalTicketNegRpa(
        response?.filter(
          (x) => x.statusNegotiation === "Aprovado - Boleto gerado"
        )?.length
      );
    } else {
      setTotalNotApprovedNegRpa(0);
      setTotalApprovedNegRpa(0);
      setTotalTicketNegRpa(0);
      setTotalAwaitingNegRpa(0);
    }
    setNegRpaIsLoading(false);
  };

  const [dados, setDadosFinanciados] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const [contratosData, setContratosData] = useState(
    localStorage.getItem("contratosAtivos")
      ? JSON.parse(localStorage.getItem("contratosAtivos"))
      : null
  );

  const [valorCustasProjuris, setValorCustasProjuris] = useState(null);
  const [valorCustas, setValorCustas] = useState(null);

  useEffect(() => {
    // const contratosData = loadContratosStorage();
    if (contratos && data) {
      const contratoLongo = contratos.find(
        (contrato) =>
          contrato.id_Contrato !== data.numero_Contrato &&
          contrato.numero_Contrato.includes(data.numero_Contrato) &&
          contrato.numero_Contrato.length === 25 &&
          contrato?.grupo?.toLowerCase()?.includes("safra")
      );
      if (
        contratoLongo &&
        contratoLongo.numero_Contrato !== data.numero_Contrato
      ) {
        setContratoLongoSafra(" / " + contratoLongo.numero_Contrato);
      } else setContratoLongoSafra("");
    }
  }, [contratos, data]);

  const handleGetUnfulfilledNegotiations = () => {
    setShowResumoNegociacaoModal(true);
  };

  const handleGetUnfulfilledAgreement = () => {
    const status = "C"; // Defina o valor do parâmetro 'status' conforme necessário
    history.push(`/acordos/visualizar?statusSelected=${status}`);
  };
  const handleGetPendingPaymentAgreement = () => {
    const status = "A"; // Defina o valor do parâmetro 'status' conforme necessário
    history.push(`/acordos/visualizar?statusSelected=${status}`);
  };
  const handleGetCyber = () => {
    history.push(`/negociar/safra`);
  };

  const countUnfulfilledNegotiations = async () => {
    const payload = {
      IdAgrupamento: data.id_Agrupamento,
      DetalheParcela: false,
      ...(data?.idLinkedGroup &&
        data?.idGrupo && {
          groupId: data?.idGrupo,
          linkedGroupId: data?.idLinkedGroup,
        }),
    };
    await GetData(payload, "getNegociacoesDatacob")
      .then((data) => {
        if (data) {
          const descumprido = data.filter((x) => x.status === "C");
          setTotalUnfulfilledNegotiations(descumprido.length);
        } else {
          setTotalUnfulfilledNegotiations(0);
        }
      })
      .catch((err) => {
        console.log(err);
      });
  };

  const countPendingPaymentAgreement = async (
    id_Agrupamento,
    numeroContrato
  ) => {
    const acordos = await getAcordos(id_Agrupamento, numeroContrato);
    const acordosManuais = await getAcordosManuais(
      id_Agrupamento,
      numeroContrato
    );
    if (acordos === null && acordosManuais === null) {
      setTotalPendingPaymentAgreement(0);
      return;
    }
    const pendente = acordos?.filter((x) => x.status === "A");
    let total = pendente.length > 0 ? pendente.length : 0;

    const totalManual = acordosManuais?.filter(
      (x) => x.status === "Aberto"
    ).length;

    total += totalManual;

    setTotalPendingPaymentAgreement(total);
  };
  const countUnfulfilledAgreement = async (id_Agrupamento, numeroContrato) => {
    const acordos = await getAcordos(id_Agrupamento, numeroContrato);
    if (acordos === null) {
      setTotalUnfulfilledAgreement(0);
      return;
    }

    const descumpridos = acordos?.filter((x) => x.status === "C");
    let total = descumpridos.length > 0 ? descumpridos.length : 0;
    setTotalUnfulfilledAgreement(total);
  };

  const GetData = async (payload, endpoint = "") => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  const getOthersContracts = async () => {
    if (!data?.id_Agrupamento) return;
    const response = await getApi(
      { idGrupo: data.id_Grupo, doc: data.cpfCnpj, crm: data.coddatacob },
      "getOutrosContratos"
    );
    if (response && response.length > 0) {
      setShowAlertOtherContract(true);
      setOtherContract(response);
    } else {
      setShowAlertOtherContract(false);
      setOtherContract([]);
    }
  };

  const getTicketsContract = async () => {
    if (!data?.id_Agrupamento) return;
    const response = await getApi(
      {
        idAgrupamento: data.id_Agrupamento,
        numeroContrato: data?.numero_Contrato,
        ...(data?.idLinkedGroup &&
          data?.idGrupo && {
            groupId: data?.idGrupo,
            linkedGroupId: data?.idLinkedGroup,
          }),
      },
      "getBoletosContrato"
    );
    updateBoletos(response);
  };

  useEffect(() => {
    async function fetchData() {
      if (data) {
        setIsLoadingCyber(true);
        setDadosFinanciados(data);
        getCustas(dados);
        //  searchStatus();
        getOthersContracts();
        getTicketsContract();

        setIsLoading(true);
        await countUnfulfilledNegotiations();
        await countUnfulfilledAgreement(
          data.id_Agrupamento,
          data.numero_Contrato
        );
        await countPendingPaymentAgreement(
          data.id_Agrupamento,
          data.numero_Contrato
        );
        setIsLoading(false);
      }
    }
    fetchData();
  }, [data]);

  useEffect(() => {
    validaConstratoElegiveis();
  }, [contratos]);

  useEffect(() => {
    setValorCustasProjuris(calcCustasPorjuris(custasProjuris));
    setValorCustas(calcCustas(custas));
  }, [custasProjuris, custas]);

  const getCustas = async () => {
    try {
      const payload = {
        IdContrato: data.id_Agrupamento || null,
        idAgrupamento: data.id_Agrupamento || null,
      };
      const response = await getApi(payload, "getcustasdatacob");
      updateCustas(response);
    } catch (error) {}
  };

  const calcCustas = (custas) => {
    let custa = 0;
    try {
      custa += custas.reduce((a, b) => a + b.vl_Despesa, 0);
    } catch (err) {
      custa = 0;
    }
    return custa;
  };

  const calcCustasPorjuris = (custas) => {
    let custa = 0;
    try {
      custa += custas.reduce((a, b) => a + (b.valor_Devido - b.valor_Pago), 0);
    } catch (err) {
      custa = 0;
    }
    return custa;
  };

  const getColorButtonCyber = () => {
    if (isLoadingCyber) return "warning text-white";
    if (hasCyber) {
      return "info";
    } else {
      return "danger";
    }
  };

  const validaConstratoElegiveis = async () => {
    setIsLoadingCyber(true);

    if (contratos?.length > 0) {
      if (contratos?.find((x) => x?.grupo?.toLowerCase()?.includes("safra"))) {
        for (const contrato of contratos) {
          const verifParc = contrato.parcelas?.find((x) => x.status === "A");
          if (verifParc === undefined) continue;
          const ret = await ConsultarContratosElegiveis(
            contrato.numero_Contrato
          );
          if (ret) {
            setHasCyber(true);
            setIsLoadingCyber(false);
            return;
          }
        }
      }
      setHasCyber(false);
    }
    setIsLoadingCyber(false);
  };

  const ConsultarContratosElegiveis = async (contrato) => {
    let ret = false;
    const payload = {
      cliente: data.cpfCnpj,
      contratos: [
        {
          grupo: "3",
          contrato: contrato,
        },
      ],
    };
    await postApi(payload, "cyberSafraconsultarElegibilidade").then((data) => {
      if (data.success) {
        ret = data.data.tiposAcordo;
      }
    });

    return ret;
  };

  const LoadNewconCota = async () => {
    setLoadDataNewcon(true);
    toast.info(
      "Processando dados Newcon! Em breve você verá os dados atualizados."
    );
    onLoadNewcon().then(() => {
      toast.success("Seus dados da Newcon estão disponíveis!");
      setLoadDataNewcon(false);
    });
  };

  return (
    <>
      <CCardBody>
        <CRow>
          <CCol>
            <CRow>
              <CCol xs="6" sm="12">
                <CLabel style={{ color: "gray" }}>Financiado</CLabel> <br />
                <CLabel>
                  <strong>
                    {dados
                      ? dados.nome + " - " + formatDocument(dados.cpfCnpj)
                      : "---"}
                  </strong>
                </CLabel>
              </CCol>
            </CRow>
            <CRow>
              <CCol xs="6" sm="6">
                <CLabel style={{ color: "gray" }}>Fase</CLabel> <br />
                <CLabel>
                  <CBadge color="primary">{dados ? dados.fase : "---"}</CBadge>
                </CLabel>
              </CCol>
              <CCol xs="6" sm="6">
                <CLabel style={{ color: "gray" }}>Contrato</CLabel> <br />
                <CLabel>
                  <strong>
                    {dados ? dados.numero_Contrato + contratoLongoSafra : "---"}
                  </strong>
                </CLabel>
              </CCol>
            </CRow>
            <CRow>
              <CCol xs="6" sm="6">
                <CLabel style={{ color: "gray" }}>Status</CLabel> <br />
                <CLabel>
                  <strong>{dados ? dados.status : "---"}</strong>
                </CLabel>
              </CCol>
              <CCol xs="6" sm="6">
                <CLabel style={{ color: "gray" }}>Grupo</CLabel> <br />
                <CLabel>
                  <strong>{dados ? dados?.grupo : "---"}</strong>
                </CLabel>
              </CCol>
            </CRow>
            <CRow>
              <CCol xs="6" sm="12">
                {/* Mudar pra 3 depois e ajeitar o estilo dos Cards */}
                <CLabel style={{ color: "gray" }}>Cliente</CLabel> <br />
                <CLabel>
                  <strong>{dados ? dados.cliente : "---"}</strong>
                </CLabel>
              </CCol>
              <CCol xs="6" sm="12">
                <CButton
                  disabled={loadDataNewcon}
                  color={loadDataNewcon ? "warn" : "primary"}
                  className="mb-2"
                  onClick={() => LoadNewconCota()}
                >
                  Carregar Newcon
                </CButton>
              </CCol>
            </CRow>
            {showAlertOtherContract && (
              <CRow>
                <CCol xs="6" sm="12">
                  {/* Mudar pra 3 depois e ajeitar o estilo dos Cards */}
                  <strong className={"text-danger"}>
                    Atenção! Este cliente possui contratos em outras carteiras.
                  </strong>{" "}
                  <CButton
                    color="danger"
                    onClick={() => setShowModalOtherContract(true)}
                  >
                    Ver Contratos
                  </CButton>
                </CCol>
              </CRow>
            )}
          </CCol>
          <CCol
            xm="6"
            md="6"
            lg="4"
            xl="3"
            style={{
              display: "flex",
              flexDirection: "column",
              paddingTop: "24px",
            }}
          >
            <div
              className="bg-danger custas-alert"
              hidden={
                valorCustas === null ||
                valorCustas === undefined ||
                isNaN(valorCustas) ||
                valorCustas === 0
              }
            >
              Atenção! Existem custas que devem ser consideradas em prováveis
              negociações! Valor de custas CRM: {formatCurrency(valorCustas)}
            </div>
            <div
              className="bg-danger custas-alert"
              hidden={
                valorCustasProjuris === null ||
                valorCustasProjuris === undefined ||
                isNaN(valorCustasProjuris) ||
                valorCustasProjuris === 0
              }
            >
              Atenção! Existem custas que devem ser consideradas em prováveis
              negociações! Valor de custas Projuris:{" "}
              {formatCurrency(valorCustasProjuris)}
            </div>
          </CCol>
          <CCol
            xm="6"
            md="6"
            lg="4"
            xl="3"
            style={{ display: "flex", flexDirection: "column" }}
          >
            <CRow style={{ flex: 1 }}>
              <CCol xs="12" md="12" className="d-grid">
                {" "}
                {/* Define uma coluna de largura total */}
                <CRow>
                  <CLabel className="" style={{ color: "gray" }}>
                    Histórico do cliente
                  </CLabel>
                </CRow>
                <CRow>
                  <CButton
                    color="danger"
                    block
                    size="sm"
                    className="mb-2"
                    onClick={() => {
                      handleGetUnfulfilledNegotiations();
                    }}
                  >
                    {isLoading && (
                      <CSpinner
                        as="span"
                        size="sm"
                        aria-hidden="true"
                        className="mr-2"
                      />
                    )}
                    {!isLoading && totalUnfulfilledNegotiations} negociações
                    descumpridas
                  </CButton>
                </CRow>
                <CRow>
                  <CButton
                    color="danger"
                    block
                    size="sm"
                    className="mb-2"
                    onClick={() => {
                      handleGetUnfulfilledAgreement();
                    }}
                  >
                    {isLoading && (
                      <CSpinner
                        as="span"
                        size="sm"
                        aria-hidden="true"
                        className="mr-2"
                      />
                    )}
                    {!isLoading && totalUnfulfilledAgreement} acordo(s)
                    descumprido(s)
                  </CButton>
                </CRow>
                <CRow>
                  <CButton
                    color="warning"
                    block
                    size="sm"
                    className="mb-2 text-white"
                    onClick={() => {
                      handleGetPendingPaymentAgreement();
                    }}
                  >
                    {isLoading && (
                      <CSpinner
                        as="span"
                        size="sm"
                        aria-hidden="true"
                        className="mr-2"
                      />
                    )}
                    {!isLoading && totalPendingPaymentAgreement} acordo(s)
                    pendente de pagamento
                  </CButton>
                </CRow>
                {(totalApprovedNegRpa !== 0 ||
                  totalNotApprovedNegRpa !== 0 ||
                  totalAwaitingNegRpa !== 0 ||
                  totalTicketNegRpa !== 0) && (
                  <>
                    <CRow>
                      <CButton
                        color="success"
                        block
                        size="sm"
                        className="mb-2"
                        onClick={() => {
                          return false;
                        }}
                      >
                        {isNegRpaLoading && (
                          <CSpinner
                            as="span"
                            size="sm"
                            aria-hidden="true"
                            className="mr-2"
                          />
                        )}
                        {!isNegRpaLoading && totalApprovedNegRpa} Negociações
                        Aprovadas - {!isNegRpaLoading && totalTicketNegRpa}{" "}
                        Boletos Gerados
                      </CButton>
                    </CRow>
                    <CRow>
                      <CButton
                        color="warning"
                        block
                        size="sm"
                        className="mb-2 text-white"
                        onClick={() => {
                          return false;
                        }}
                      >
                        {isNegRpaLoading && (
                          <CSpinner
                            as="span"
                            size="sm"
                            aria-hidden="true"
                            className="mr-2"
                          />
                        )}
                        {!isNegRpaLoading && totalAwaitingNegRpa} Negociações
                        Pendentes
                      </CButton>
                    </CRow>
                    <CRow>
                      <CButton
                        color="danger"
                        block
                        size="sm"
                        className="mb-2"
                        onClick={() => {
                          return false;
                        }}
                      >
                        {isNegRpaLoading && (
                          <CSpinner
                            as="span"
                            size="sm"
                            aria-hidden="true"
                            className="mr-2"
                          />
                        )}
                        {!isNegRpaLoading && totalNotApprovedNegRpa} Negociações
                        Reprovadas/Falha
                      </CButton>
                    </CRow>
                  </>
                )}
              </CCol>
            </CRow>
            <CRow className="justify-content-end">
              {!isTelaprincipal && (
                <CButton
                  to="/telaprincipal"
                  className="btn btn-warning text-white mx-2 mt-2"
                >
                  Ir para Tela Principal
                </CButton>
              )}
            </CRow>
            {/* <CRow className="d-flex justify-content-end">
              {isTelaprincipal && (
              <CButton
                to="/telaprincipal"
                block
                className="btn btn-warning text-white mt-2"
              >
                Acordo pendente de aprovacao BBC
              </CButton>
            )}
          </CRow> */}
          </CCol>
        </CRow>

        {data?.grupo?.toLowerCase()?.includes("safra") && (
          <CRow>
            <CButton
              color={getColorButtonCyber()}
              block
              size="sm"
              className="ml-3"
              style={{ width: "40%" }}
              onClick={() => {
                handleGetCyber();
              }}
            >
              {isLoadingCyber && (
                <CSpinner
                  as="span"
                  size="sm"
                  aria-hidden="true"
                  className="mr-2"
                />
              )}
              {isLoadingCyber && "Consultando Elegibilidade - Cyber Safra"}
              {!isLoadingCyber && hasCyber && "Contrato Elegível - Safra"}
              {!isLoadingCyber && !hasCyber && "Contrato Não Elegível - Cyber"}
            </CButton>
          </CRow>
        )}
        {(nrFaseBusca > 0 || showAlertJuri) && (
          <CRow className={"float-right m-2 text-danger"}>
            <strong>
              Atenção Operador! Existem indicativos de Busca e Apreensão
            </strong>
          </CRow>
        )}
        {showResumoNegociacaoModal && (
          <NegociacaoResumoModal
            isOpen={showResumoNegociacaoModal}
            onClose={() => setShowResumoNegociacaoModal(false)}
            statusActive="C"
          />
        )}
        {showModalOtherContract && (
          <OutrosContratos
            isOpen={showModalOtherContract}
            onClose={() => setShowModalOtherContract(false)}
            otherContract={otherContract}
          />
        )}
      </CCardBody>
    </>
  );
};

export default DadosContratos;
