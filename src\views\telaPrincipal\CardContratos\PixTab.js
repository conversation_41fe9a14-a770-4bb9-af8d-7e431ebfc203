import React, { useEffect, useState } from "react";
import { CCardBody } from "@coreui/react";
import { GET_DATA } from "src/api";
import { useMyContext } from "src/reusable/DataContext";
import { toast } from "react-toastify";
import DetalhesBoletoModal from "src/views/acordos/DetalhesBoletoModal";
import { getApi } from "src/reusable/functions";
import LoadingComponent from "src/reusable/Loading";
import TablePix from "./Parcial/TablePix";

const PixTab = ({ selected }) => {
  const { data } = useMyContext();

  const [loading, setLoading] = useState(false);
  const [showModalDetalhesBoleto, setShowModalDetalhesBoleto] = useState(false);
  const [tablePix, setTablePix] = useState([]);

  const getData = async () => {
    setLoading(true);
    const bol = await getApi(
      {
        idAgrupamento: data?.id_Agrupamento,
      },
      "getPixContrato"
    );
    const sortedBol = bol?.sort((a, b) => {
      if (a.status === "A" && b.status !== "A") return -1;
      if (a.status !== "A" && b.status === "A") return 1;
      if (a.status === "P" && b.status !== "P") return -1;
      if (a.status !== "P" && b.status === "P") return 1;
      return 0;
    });
    setTablePix(sortedBol?.length > 0 ? sortedBol : []);
    setLoading(false);
  };

  useEffect(() => {
    if (selected) getData();
  }, [selected]);

  return (
    <CCardBody>
      {loading && <LoadingComponent />}
      {!loading && (
        <>
          <TablePix tablePix={tablePix} getDataPix={getData} itemPerPage={4} />
        </>
      )}
    </CCardBody>
  );
};

export default PixTab;
