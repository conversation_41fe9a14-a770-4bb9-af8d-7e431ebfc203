import React, { useState } from "react";
import { CDataTable, CButton, CInput, CSelect } from "@coreui/react";
import CIcon from "@coreui/icons-react";
import { cilSearch } from "@coreui/icons";
import {
  formatDateTime,
  formatPhone,
  formatCpfCnpj,
} from "../utils/helpers.tsx";
import ClientModal from "./clientModal.tsx";
import * as XLSX from "xlsx";

interface TableRegistrosProps {
  clients: any[];
}

const TableRegistros: React.FC<TableRegistrosProps> = ({ clients }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [clientSelecionado, setClientSelecionado] = useState(null);

  const fields = [
    { key: "document", label: "Documento" },
    { key: "name", label: "Nome" },
    { key: "primaryEmail", label: "E-mail" },
    { key: "primaryPhone", label: "Telefone" },
    { key: "createdAt", label: "Data do cadastro" },
    { key: "updatedAt", label: "Data de atualização" },
    { key: "status", label: "Status" },
    { key: "acao", label: "Ação", _style: { width: "60px" } },
  ];

  const handleOpen = (client: any) => {
    setClientSelecionado(client);
    setModalVisible(true);
  };

  const formatStatus = (text: string) => {
    if (!text) return "";
    return text
      .replace(/([a-z])([A-Z])/g, "$1 $2") // separa camelCase
      .replace(/^./, (str) => str.toUpperCase()); // primeira letra maiúscula
  };

  const [filterColumn, setFilterColumn] = useState("name");
  const [filterValue, setFilterValue] = useState("");
  const getUpdatedOrCreated = (item: any) =>
    item.updatedAt ? new Date(item.updatedAt) : new Date(item.createdAt);

  const filteredClients = clients
    .slice()
    .sort((a, b) => {
      const dateA = getUpdatedOrCreated(a);
      const dateB = getUpdatedOrCreated(b);

      const diff = dateB.getTime() - dateA.getTime(); // mais recente primeiro
      if (diff !== 0) return diff;

      return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
    })
    .filter((c) => {
      const value = (c[filterColumn] || "").toString().toLowerCase();
      return value.includes(filterValue.toLowerCase());
    });

  const exportToExcel = () => {
    const dadosParaExportar = filteredClients.length > 0 ? filteredClients : [];

    if (dadosParaExportar.length === 0) return;

    const dados = dadosParaExportar.map((c) => ({
      Documento: formatCpfCnpj(c.document),
      Nome: c.name,
      Email: c.primaryEmail,
      Telefone: formatPhone(c.primaryPhone),
      "Data do cadastro": formatDateTime(c.createdAt),
      "Data de atualização": formatDateTime(c.updatedAt || c.createdAt),
      Status: formatStatus(c.status),
    }));

    const worksheet = XLSX.utils.json_to_sheet(dados);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "Registros");

    XLSX.writeFile(workbook, `registros_${new Date().toISOString()}.xlsx`);
  };

  return (
    <>
      <div className="row mb-3 align-items-end">
        <div className="col-auto">
          <label className="form-label">Filtrar por</label>
          <CSelect
            value={filterColumn}
            onChange={(e: React.ChangeEvent<HTMLSelectElement>) =>
              setFilterColumn(e.target.value)
            }
          >
            <option value="document">Documento</option>
            <option value="name">Nome</option>
            <option value="primaryEmail">E-mail</option>
            <option value="primaryPhone">Telefone</option>
            <option value="status">Status</option>
          </CSelect>
        </div>

        <div className="col-auto">
          <CInput
            placeholder="Digite para filtrar..."
            value={filterValue}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setFilterValue(e.target.value)
            }
          />
        </div>

        <div className="col d-flex justify-content-end">
          <div className="d-flex flex-column">
            <label className="form-label">&nbsp;</label>
            <CButton color="success" onClick={exportToExcel}>
              Exportar Excel
            </CButton>
          </div>
        </div>
      </div>

      <CDataTable
        items={filteredClients}
        fields={fields}
        striped
        hover
        sorter
        pagination
        itemsPerPage={10}
        scopedSlots={{
          document: (item) => <td>{formatCpfCnpj(item.document)}</td>,
          primaryPhone: (item) => <td>{formatPhone(item.primaryPhone)}</td>,
          createdAt: (item) => <td>{formatDateTime(item.createdAt)}</td>,
          updatedAt: (item) => (
            <td>{formatDateTime(item.updatedAt || item.createdAt)}</td>
          ),
          status: (item) => <td>{formatStatus(item.status)}</td>,
          acao: (item) => (
            <td className="text-center">
              <CButton color="info" size="sm" onClick={() => handleOpen(item)}>
                <CIcon content={cilSearch} />
              </CButton>
            </td>
          ),
        }}
      />

      <ClientModal
        client={clientSelecionado}
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default TableRegistros;
