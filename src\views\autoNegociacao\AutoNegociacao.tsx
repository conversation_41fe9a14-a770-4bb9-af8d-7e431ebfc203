import React, { ReactNode } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@coreui/react";
import {
  AutoNegociacaoProvider,
  useAutoNegociacao,
} from "./pageContext/AutoNegociacaoContext.tsx";

interface AutoNegociacaoProps {
  children: (props: { clients: any[] }) => ReactNode;
  title?: string;
}

const AutoNegociacaoInner: React.FC<AutoNegociacaoProps> = ({
  children,
  title,
}) => {
  const { clients } = useAutoNegociacao();

  if (!clients || clients.length === 0) {
    return (
      <div className="text-center my-5">
        <CSpinner color="primary" />
        <div className="mt-2">Carregando dados...</div>
      </div>
    );
  }

  return (
    <CCard className="mb-4 bg-transparent border-0 shadow-none text-dark">
      {title && (
        <CCardHeader className="bg-transparent border-0 text-dark">
          {title}
        </CCardHeader>
      )}
      <CCardBody className="bg-transparent text-dark">
        {children({ clients })}
      </CCardBody>
    </CCard>
  );
};

const AutoNegociacao: React.FC<AutoNegociacaoProps> = (props) => (
  <AutoNegociacaoProvider>
    <AutoNegociacaoInner {...props} />
  </AutoNegociacaoProvider>
);

export default AutoNegociacao;
