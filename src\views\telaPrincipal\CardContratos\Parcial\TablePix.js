import { CBadge, CButton, CDataTable } from "@coreui/react";
import React, { useState } from "react";
import { formatDate, formatThousands } from "src/reusable/helpers";
import CancelarPixModal from "./CancelarPixModal";

const TablePix = ({ tablePix, itemPerPage = null, getDataPix }) => {
  const pixFields = [
    { key: "status" },
    {
      key: "dtCadastro",
      label: "Data Pix",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "valor",
      label: "Valor Negociação",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "dtCancelamento",
      label: "Dt Cancelamento",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "descricaoCancelamento",
      label: "Descrição Motivo Cancelamento",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "userNegociacao",
      label: "Nome do Negociador",
      _style: { whiteSpace: "nowrap" },
    },
    { label: "Ações", key: "acoes" },
  ];

  const [showCancelarPixModal, setShowCancelarPixModal] = useState(false);
  const [itemCancelarPix, setItemCancelarPix] = useState(null);

  const renderStatus = (status) => {
    switch (status) {
      case "P":
        return <CBadge color="success">Pago</CBadge>;
      case "A":
        return <CBadge color="info">Aberto</CBadge>;
      case "C":
        return <CBadge color="danger">Cancelado</CBadge>;
      default:
        break;
    }
  };

  const handleCancelarPix = (item) => {
    setShowCancelarPixModal(true);
    setItemCancelarPix(item);
  };

  const [copied, setCopied] = useState(false);
  const handleCopiarPix = (item) => {
    navigator.clipboard.writeText(item.copiaCola);
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 1000);
  };

  return (
    <>
      <CDataTable
        items={tablePix}
        fields={pixFields}
        pagination={itemPerPage !== null}
        itemsPerPage={itemPerPage}
        hover
        responsive
        scopedSlots={{
          dtCadastro: (item) =>
            item.dtCadastro ? (
              <td>{formatDate(item.dtCadastro)}</td>
            ) : (
              <td>---</td>
            ),
          valor: (item) =>
            item.valor ? <td>{formatThousands(item.valor)}</td> : <td>---</td>,
          status: (item) => <td> {renderStatus(item.status)} </td>,
          descricaoCancelamento: (item) => (
            <td className="nowrap-cell">{item.descricaoCancelamento}</td>
          ),
          dtCancelamento: (item) =>
            item.dtCancelamento ? (
              <td>{formatDate(item.dtCancelamento)}</td>
            ) : (
              <td>---</td>
            ),

          userNegociacao: (item) => <td>{item.userNegociacao ?? "---"}</td>,
          acoes: (item) => (
            <td>
              {item.status === "A" && (
                <>
                  <CButton
                    className="btn-warning py-1 px-1 mr-2 text-white"
                    onClick={() => handleCopiarPix(item)}
                  >
                    {copied ? "Copiado!" : "Copiar"}
                  </CButton>
                  <CButton
                    className="btn-danger py-1 px-1"
                    onClick={() => handleCancelarPix(item)}
                  >
                    Cancelar
                  </CButton>
                </>
              )}
            </td>
          ),
        }}
      />
      {showCancelarPixModal && (
        <CancelarPixModal
          isOpen={showCancelarPixModal}
          onClose={() => setShowCancelarPixModal(false)}
          item={itemCancelarPix}
          getDataPix={getDataPix}
        />
      )}
    </>
  );
};

export default TablePix;
