import { GET_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";

const autoNegociacaoEndpoints = {
  getClientsWithContracts: "/api/acordoonline/clients/with-contracts",
}

let cachedBaseURL = null;
const getBaseURLFromAPI = async () => {
  if (cachedBaseURL) return cachedBaseURL;

  const configKey = "url_ms_site_gvc";

  try {
    const response = await GET_DATA(getURI("getConfigByKey"), null, true, true, configKey);
    if (!response || typeof response !== "string") {
      throw new Error("URL não encontrada ou inválida.");
    }

    const urlFinal =
      window.location.protocol === "https:" ? response.replace("http://", "https://") : response;

    cachedBaseURL = urlFinal;
    return cachedBaseURL;
  } catch (error) {
    console.error("Erro ao buscar baseURL do AutoNegociacao:", error);
    throw error;
  }
};

const getAutoNegociacaoURI = async (endpointKey = null) => {
  const baseURL = await getBaseURLFromAPI();
  if (!endpointKey) return baseURL;

  const endpointPath = autoNegociacaoEndpoints[endpointKey];
  if (!endpointPath) {
    throw new Error(`Endpoint "${endpointKey}" não encontrado.`);
  }
  return `${baseURL}${endpointPath}`;
};

const fetchAutoNegociacao = async ({
  endpointKey,
  method = "GET",
  body = null
}) => {
  const token = window.localStorage.getItem("token");
  const url = await getAutoNegociacaoURI(endpointKey);
  const headers = {
    Accept: "application/json",
    Authorization: token,
    "Content-Type": "application/json",
  };
  const options = {
    method,
    headers,
  };

  if (body && method !== "GET") {
    options.body = JSON.stringify(body);
  }

  const response = await fetch(url, options);

  if (!response.ok) {
    throw new Error(
      `Erro na requisição ${method} ${url}: ${response.status} ${response.statusText}`
    );
  }

  return await response.json();
};

export { getAutoNegociacaoURI, fetchAutoNegociacao };
