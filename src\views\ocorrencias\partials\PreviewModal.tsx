import {
  CModal,
  <PERSON>utton,
  CModal<PERSON>ooter,
  CModalHeader,
  CModalBody,
} from "@coreui/react";
import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";
import { postApi } from "src/reusable/functions";

type PreviewData = {
  url: string;
  title: string;
  contractNumber?: string;
};

type Props = {
  isOpen: boolean;
  onClose: () => void;
  previews: PreviewData[];
  bodyPreviews: any[];
};

const PreviewModal = ({ isOpen, onClose, previews, bodyPreviews }: Props) => {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (isOpen && previews.length > 0) {
      setCurrentIndex(0);
    }
  }, [isOpen, previews]);

  useEffect(() => {
    if (!isOpen) {
      previews.forEach((preview) => {
        if (preview.url) {
          URL.revokeObjectURL(preview.url);
        }
      });
    }
  }, [isOpen, previews]);

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : previews.length - 1));
  };

  const handleNext = () => {
    setCurrentIndex((prev) => (prev < previews.length - 1 ? prev + 1 : 0));
  };

  const currentPreview = previews[currentIndex];

  const handleKeyDown = (event: KeyboardEvent) => {
    switch (event.key) {
      case "ArrowLeft":
        handlePrevious();
        break;
      case "ArrowRight":
        handleNext();
        break;
      default:
        break;
    }
  };

  useEffect(() => {
    window.addEventListener("keydown", handleKeyDown);

    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  if (!isOpen || previews.length === 0) {
    return null;
  }

  const handleGenerate = async () => {
    setIsLoading(true);
    console.log(bodyPreviews);
    for (const bodyPreview of bodyPreviews) {
      try {
        const pedidoResponse = await postApi(
          {
            idOperador: bodyPreview?.userId,
            idFinanciado: bodyPreview?.idFinanciado,
            idContrato: bodyPreview?.idContrato,
            idGrupo: bodyPreview?.idGrupo,
            crm: bodyPreview?.activeConnection,
            tipo: 2,
          },
          "postPedidoCartasEtermos"
        );
        if (
          pedidoResponse?.success === true &&
          pedidoResponse?.data?.id !== undefined
        ) {
          const cartaResponse = await postApi(
            {
              pedidoId: pedidoResponse.data.id,
              mesesParcelas: bodyPreview?.mesesParcelas,
              dataRetornoPagamento: bodyPreview?.dataRetornoPagamento,
              clientePrincipal: bodyPreview?.clientePrincipal,
              adversoPrincipal: bodyPreview?.adversoPrincipal,
              grupoCotaContrato: bodyPreview?.grupoCotaContrato,
            },
            "postCartaDiluicaoInfos"
          );
          if (cartaResponse?.success === true) {
            toast.success(
              "Pedido enviado com sucesso! Contrato:" +
                bodyPreview.grupoCotaContrato
            );
          } else {
            toast.error(
              "Erro ao gerar carta! Contrato:" + bodyPreview.grupoCotaContrato
            );
          }
        } else {
          toast.error(
            "Erro ao gerar pedido de carta! Contrato:" +
              bodyPreview.grupoCotaContrato
          );
        }
      } catch (err) {
        console.error(err);
        toast.error(
          "Erro ao gerar carta! Contrato:" + bodyPreview.grupoCotaContrato
        );
      }
    }
    onClose();
    setIsLoading(false);
  };

  return (
    <CModal
      show={isOpen}
      onClose={onClose}
      closeOnBackdrop={false}
      className="preview-modal"
      centered
      size="xl"
    >
      <CModalHeader closeButton>
        <div className="d-flex align-items-center justify-content-between w-100">
          <h5 className="mb-0">
            {currentPreview?.title || "Preview do Documento"}
            {currentPreview?.contractNumber && (
              <span className="text-muted ms-2">
                - Contrato: {currentPreview.contractNumber}
              </span>
            )}
          </h5>
          {previews.length > 1 && (
            <div className="d-flex align-items-center">
              <CButton
                color="light"
                size="sm"
                onClick={handlePrevious}
                className="me-2"
                disabled={previews.length <= 1}
              >
                <i className="cil-arrow-left"></i>
              </CButton>
              <span className="mx-2 text-muted">
                {currentIndex + 1} de {previews.length}
              </span>
              <CButton
                color="light"
                size="sm"
                onClick={handleNext}
                disabled={previews.length <= 1}
              >
                <i className="cil-arrow-right"></i>
              </CButton>
            </div>
          )}
        </div>
      </CModalHeader>
      <CModalBody style={{ padding: "0", height: "80vh" }}>
        {currentPreview?.url && (
          <iframe
            src={currentPreview.url}
            style={{
              width: "100%",
              height: "100%",
              border: "none",
            }}
            title={currentPreview.title || "Preview do Documento"}
          />
        )}
      </CModalBody>
      <CModalFooter>
        <div className="d-flex justify-content-between w-100">
          <div>
            {previews.length > 1 && (
              <small className="text-muted">
                Use as setas para navegar entre os previews
              </small>
            )}
          </div>
          <div>
            <CButton color="secondary" onClick={onClose}>
              Fechar Preview
            </CButton>

            <CButton
              color="success"
              onClick={handleGenerate}
              disabled={isLoading}
              className="ml-2"
            >
              {isLoading ? "Gerando..." : "Gerar Carta"}
            </CButton>
          </div>
        </div>
      </CModalFooter>
    </CModal>
  );
};

export default PreviewModal;
