import React, { useState } from "react";
import {
  CModal<PERSON>ooter,
  CModal,
  CModalHeader,
  CModalTitle,
  CModalBody,
  CButton,
  CFormGroup,
  CLabel,
  CInput,
} from "@coreui/react";
import CardLoading from "src/reusable/CardLoading";
import { postApi } from "src/reusable/functions";
import { toast } from "react-toastify";
import { isEmailValid } from "src/reusable/helpers";

const ControleUsuariosEmailModal = ({
  isOpen,
  onClose,
  tickets = null,
  pix = null,
  type = null,
}) => {
  const [loading, setLoading] = useState(false);
  const [dest, setDest] = useState("");
  // const [rem, setRem] = useState("");
  const [assunto, setAssunto] = useState("");
  const [mensagem, setMensagem] = useState("");

  const handleDest = (e) => {
    setDest(e.target.value);
  };
  // const handleRem = (e) => {
  //   setRem(e.target.value);
  // };
  const handleAssunto = (e) => {
    setAssunto(e.target.value);
  };
  const handleMensagem = (e) => {
    setMensagem(e.target.value);
  };

  const handleEnviar = async () => {
    if (dest === "" || assunto === "" || mensagem === "") {
      toast.error("Preencha todos os campos!");
      return;
    }
    if (!isEmailValid(dest)) {
      toast.error("Destinatário inválido!");
      return;
    }
    setLoading(true);
    const payload = {
      destinatario: dest,
      assunto: assunto,
      mensagem: mensagem,
      dataExcel: type === "B" ? tickets : pix,
    };
    try {
      const res = await postApi(
        payload,
        type === "B"
          ? "postControleUsuarioSendEmail"
          : "postControleUsuarioPixSendEmail"
      );
      if (res?.success) {
        toast.success("Email Enviado com Sucesso!");
        onClose();
      } else {
        toast.error("Erro ao enviar Email!");
      }
    } catch (error) {
      console.log(error);
      toast.error("Erro ao enviar Email!");
    }
    setLoading(false);
  };

  return (
    <CModal show={isOpen} onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>
        <CModalTitle>Envio de E-mail</CModalTitle>
      </CModalHeader>
      {loading && (
        <CModalBody style={{ minHeight: "470px" }}>
          <CardLoading />{" "}
        </CModalBody>
      )}
      {!loading && (
        <CModalBody>
          <CFormGroup>
            {/* <CLabel>Remetente </CLabel>
            <CInput value={rem} type="text" onChange={handleRem} /> */}
            <CLabel>Destinatário </CLabel>
            <CInput value={dest} type="text" onChange={handleDest} />
            <CLabel>Assunto </CLabel>
            <CInput value={assunto} type="text" onChange={handleAssunto} />
            <CLabel>Mensagem</CLabel>
            <textarea
              rows={4}
              className="form-control"
              value={mensagem}
              onChange={handleMensagem}
            ></textarea>
          </CFormGroup>
        </CModalBody>
      )}
      <CModalFooter>
        <CButton disabled={loading} color="danger" onClick={onClose}>
          Fechar
        </CButton>
        <CButton disabled={loading} color="success" onClick={handleEnviar}>
          Enviar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default ControleUsuariosEmailModal;
