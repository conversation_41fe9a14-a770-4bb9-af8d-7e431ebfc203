import {
  CModal,
  <PERSON>utton,
  <PERSON><PERSON>odal<PERSON>ooter,
  CModalHeader,
  CModalTitle,
  CInput,
} from "@coreui/react";
import React, { useState } from "react";
import { toast } from "react-toastify";
import { postApiQueryFile } from "src/reusable/functions";
import PreviewModal from "./PreviewModal.tsx";

type InputValues = {
  [key: number]: {
    periodoParcelas: string;
    dataRetorno: string;
  };
};

type ActiveContract = {
  numero_Contrato: string | null;
  id_Contrato: number | null;
  grupoId: number | null;
};

type Props = {
  isOpen: boolean;
  onClose: () => void;
  contratosAtivos: ActiveContract[] | null;
};

const GerarCartaDiluicaoModal = ({
  isOpen,
  onClose,
  contratosAtivos,
}: Props) => {
  const [selectedContracts, setSelectedContracts] = useState<string[]>([]);

  const [inputValues, setInputValues] = useState<InputValues>({});
  const handleInputChange = (
    event: React.FormEvent<HTMLInputElement>,
    idContract: number
  ) => {
    const { value } = event.currentTarget;
    const newInputValues = {
      [idContract]: {
        ...inputValues[idContract],
        [event.currentTarget.name]: value,
      },
    };

    setInputValues((prevValues) => ({
      ...prevValues,
      ...newInputValues,
    }));
  };

  const renderFieldsForSelectedContracts = (idContract: number) => {
    return (
      <div className="row ml-3 mt-2 mb-3" key={idContract}>
        <CInput
          type="text"
          name="periodoParcelas"
          placeholder="Período das parcelas diluídas"
          className="col-md-5 mr-2"
          onChange={(e) => handleInputChange(e, idContract)}
          value={inputValues[idContract]?.periodoParcelas}
        />
        <CInput
          type="text"
          name="dataRetorno"
          placeholder="Data de retorno do pagamento"
          className="col-md-5"
          onChange={(e) => handleInputChange(e, idContract)}
          value={inputValues[idContract]?.dataRetorno}
        />
      </div>
    );
  };

  const handleCheckboxChange = (event: React.FormEvent<HTMLInputElement>) => {
    const { checked, value } = event.currentTarget;
    if (checked) {
      setSelectedContracts([...selectedContracts, value]);
    } else {
      setSelectedContracts(selectedContracts.filter((item) => item !== value));
    }
  };

  const userLogged = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const [isLoading, setIsLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [previews, setPreviews] = useState([]);
  const [bodyPreviews, setBodyPreviews] = useState([]);
  const handleSubmit = async () => {
    setIsLoading(true);
    const newPreviews = [];
    const newBodyPreviews = [];

    for (const selected of selectedContracts) {
      try {
        const active = contratosAtivos?.find(
          (x) => x.numero_Contrato === selected
        );
        if (!active) {
          toast.error("Não foi possível gerar o documento");
          continue;
        }

        const bodyPreview = {
          crm: userLogged?.activeConnection,
          idGrupo: active?.grupoId,
          clientePrincipal: financiadoData?.cliente?.trim(),
          adversoPrincipal: financiadoData?.nome,
          grupoCotaContrato: selected,
          mesesParcelas: inputValues[active?.id_Contrato]?.periodoParcelas,
          dataRetornoPagamento: inputValues[active?.id_Contrato]?.dataRetorno,
          idContrato: active?.id_Contrato,
          idFinanciado: financiadoData?.id_Financiado,
          userId: userLogged?.id,
        };

        const previewResponse = await postApiQueryFile(
          "postPedidoCartasEtermosGenerateCartaDiluicaoPreview",
          "",
          bodyPreview
        );

        if (previewResponse.ok) {
          const blob = await previewResponse.blob();
          if (blob.type === "application/pdf" || blob.size > 0) {
            const url = URL.createObjectURL(blob);
            newPreviews.push({
              url,
              title: "Carta de Diluição",
              contractNumber: selected,
            });
            newBodyPreviews.push(bodyPreview);
          } else {
            toast.error(
              `Não foi possível gerar o preview do contrato ${selected}`
            );
          }
        } else {
          toast.error(`Erro ao gerar preview do contrato ${selected}`);
        }
      } catch (err) {
        console.error(err);
        toast.error(`Erro ao processar contrato ${selected}`);
      }
    }

    if (newPreviews.length > 0) {
      setPreviews(newPreviews);
      setBodyPreviews(newBodyPreviews);
      setShowPreview(true);
    } else {
      toast.error("Nenhum preview foi gerado com sucesso");
    }

    setIsLoading(false);
  };

  const handleClosePreview = () => {
    // URLs will be cleaned up by the PreviewModal component
    setPreviews([]);
    setShowPreview(false);
  };

  return (
    <CModal
      className="modal custom-modal"
      show={isOpen}
      onClose={onClose}
      size="lg"
      closeOnBackdrop={false}
    >
      <CModalHeader>
        <CModalTitle>Gerar Carta de Diluição</CModalTitle>
      </CModalHeader>
      <div className="m-4">
        <div className="row mb-3">
          <div className="col">
            <h6 className="mb-0">Selecione os contratos desejados:</h6>
          </div>
        </div>
        <div className="col">
          {contratosAtivos?.map((item: ActiveContract, index: React.Key) => (
            <>
              <div key={index} className="row align-items-center mb-2">
                <div className="col-auto">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      name="checkboxTel"
                      id={`checkboxTel${index}`}
                      value={item?.numero_Contrato}
                      onChange={handleCheckboxChange}
                      checked={selectedContracts.includes(
                        item?.numero_Contrato
                      )}
                    />
                    <label
                      className="form-check-label"
                      htmlFor={`checkboxTel${index}`}
                    >
                      {item?.numero_Contrato}
                    </label>
                  </div>
                </div>
              </div>
              {selectedContracts.includes(item?.numero_Contrato) &&
                renderFieldsForSelectedContracts(item?.id_Contrato)}
            </>
          ))}
        </div>
      </div>
      <CModalFooter>
        <CButton color="danger" onClick={onClose}>
          Sair
        </CButton>
        <CButton color="success" onClick={handleSubmit} disabled={isLoading}>
          {isLoading ? "Gerando..." : "Confirmar"}
        </CButton>
      </CModalFooter>

      {showPreview && (
        <PreviewModal
          isOpen={showPreview}
          onClose={handleClosePreview}
          previews={previews}
          bodyPreviews={bodyPreviews}
        />
      )}
    </CModal>
  );
};

export default GerarCartaDiluicaoModal;
