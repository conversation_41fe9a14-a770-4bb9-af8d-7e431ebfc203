import React, { useState, useEffect } from "react";
import {
  CButton,
  CModal,
  CModalHeader,
  CModalBody,
  CModalFooter,
  CRow,
  CCol,
  CLabel,
  CInput,
  CSpinner,
} from "@coreui/react";
import { formatCurrency, formatDate } from "src/reusable/helpers";
import Select from "react-select";
import { useMyContext } from "src/reusable/DataContext";
import ReactDatePicker from "react-datepicker";
import { ptBR } from "date-fns/locale";
import { format } from "date-fns";
import { GET_DATA, POST_DATA } from "src/api";
import { toast } from "react-toastify";
import { getURI } from "src/config/apiConfig";
import { formatarTelefone } from "src/reusable/functions";
import CardLoading from "src/reusable/CardLoading";
import LoadingComponent from "src/reusable/Loading";

const minEntradaPerc = 15;
const contratoSelect = [
  "DB",
  "CONTRATO VENDA",
  "CONFISSÃO DE DIVIDA",
  "CONFISSÃO DE CUSTA",
  "CONFISSÃO DE TAXA",
  "CONFISSÃO DE DB",
];
const today = new Date();

const CalcTipoUmRNI = ({ isOpen, onClose }) => {
  const [minEntrada, setMinEntrada] = useState(0);

  const [optionsContract, setOptionsContract] = useState([]);
  const [selectedContract, setSelectedContract] = useState(null);

  const [optionsSelectContracts, setOptionsSelectContracts] = useState([]);
  const [selectedCalculatedContract, setSelectedCalculatedContract] =
    useState(null);
  const [idOcorrenciaSistema, setIdOcorrenciaSistema] = useState(null);
  const [isLoading, setIsLoading] = useState(false);

  const [objValue, setObjValue] = useState({
    contrato: null,
    dataVencimento: new Date(),
    entrada: 0,
    vincenda: 0,
    qtdParcelas: 0,
    dataEntrada: new Date(),
    parcelaInicial: "",
    parcelaFinal: "",
    totalAtualizado: 0,
  });

  const [totalAtualizadoNovo, setTotalAtualizadoNovo] = useState(0);
  const [valorParcela, setValorParcela] = useState(0);

  const [selectedRetornoTelefone, setSelectedRetornoTelefone] = useState({
    label: "Selecione",
    value: "",
  });
  const [optionsTelefoneRetorno, setOptionsTelefoneRetorno] = useState([
    {
      label: "Selecione",
      value: "",
    },
  ]);

  async function getConfigPercetualValorEntrada() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "percetual_valor_entrada_calculadora_rni"
    );
    try {
      return JSON.parse(response);
    } catch (err) {
      return 15;
    }
  }

  async function getTotalAtualizado() {
    if (selectedCalculatedContract == null) {
      console.log("contrato nao selecionado");
      return 0;
    }

    let contratos = contratosAtivos.find(
      (contrato) =>
        contrato.numero_Contrato === selectedCalculatedContract.value
    );

    let parcelas = contratos.parcelas.filter((item) => item.status == "A");
    parcelas = parcelas.filter(
      (item) => item.nome_Tipo_Parcela !== "SALDO_VINCENDO"
    );

    parcelas = parcelas.map((pItem) => {
      if (pItem.atraso === 0) {
        pItem.vl_Atualizado = pItem.vl_Original;
        pItem.vl_Desc_Max = pItem.vl_Original;
      }
      return pItem;
    });

    const somaAtualizados = parcelas.reduce((accumulator, currentItem) => {
      if (
        currentItem.vl_Atualizado == null ||
        currentItem.vl_Atualizado == undefined ||
        isNaN(currentItem.vl_Atualizado)
      ) {
        return accumulator + currentItem.vl_Saldo_Atualizado;
      }
      return accumulator + currentItem.vl_Atualizado;
    }, 0);

    if (somaAtualizados >= 0) {
      setObjValue({ ...objValue, totalAtualizado: somaAtualizados });
    }
    calculaMinEntrada(somaAtualizados);
  }

  async function calculaMinEntrada(valortotal) {
    setIsLoading(true);
    let minEntPerc = await getConfigPercetualValorEntrada();
    let minEntCal = parseFloat((valortotal * (minEntPerc / 100)).toFixed(2));
    setMinEntrada(parseFloat(minEntCal));
    setIsLoading(false);
  }

  const validateFields = () => {
    if (objValue?.parcelaInicial === "") {
      alert("Preencha a Parcela Inicial ");
      return false;
    }
    if (objValue?.parcelaFinal === "") {
      alert("Preencha a Parcela Final ");
      return false;
    }

    if (objValue?.entrada < minEntrada) {
      alert(
        "O Valor da Entrada, não pode ser menos que o Valor Mínimo de Entrada:"
      );
      return false;
    }
    return true;
  };

  function montarDescricaoHistorico() {
    let parcelaInicial = format(objValue?.parcelaInicial, "MM/yyyy", {
      locale: ptBR,
    });
    let parcelaFinal = format(objValue?.parcelaFinal, "MM/yyyy", {
      locale: ptBR,
    });
    let dataEntrada = format(objValue?.dataEntrada, "dd/MM/yyyy", {
      locale: ptBR,
    });
    let dataVencimento = format(objValue?.dataVencimento, "dd/MM/yyyy", {
      locale: ptBR,
    });
    return (
      `CONTRATO SELECIONADO ${selectedCalculatedContract.value}; CONTRATO: ${
        objValue?.contrato
      }; PARCELAS NEGOCIADAS: ${parcelaInicial} a ${parcelaFinal}; VALOR ATUALIZADO: R$ ${formatCurrency(
        objValue?.totalAtualizado
      )}; VALOR ENTRADA: ${formatCurrency(objValue?.entrada)}; ` +
      `VINCENDA: ${formatCurrency(
        objValue?.vincenda
      )}; DATA ENTRADA : ${dataEntrada}; QUANTIDADE DE PARCELAS: ${
        objValue?.qtdParcelas
      }; VALOR DE PARCELA: ${formatCurrency(
        valorParcela ?? 0
      )}; 1º VENCIMENTO: ${dataVencimento}`
    );
  }

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const contratosAtivos = localStorage.getItem("contratosAtivos")
    ? JSON.parse(localStorage.getItem("contratosAtivos"))
    : null;

  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : null;

  async function getIdOcorrencia() {
    const tiposOcorrencia = await GET_DATA("Datacob/Ocorrencias");
    let itemOcorrencia = tiposOcorrencia.find(
      (item) => item["cod_Ocorr_Sistema"] === "011"
    );
    if (itemOcorrencia) {
      setIdOcorrenciaSistema(itemOcorrencia.id_Ocorrencia_Sistema);
    }
  }
  async function historicoAdicionar() {
    if (!validateFields()) return false;

    if (selectedRetornoTelefone.value === "") {
      toast.error("Selecione um telefone");
      return false;
    }
    let telefone = selectedRetornoTelefone.value;
    const data = {
      login: user?.username,
      id_Contrato: financiadoData?.id_Contrato,
      id_Ocorrencia_Sistema: idOcorrenciaSistema,
      observacao: montarDescricaoHistorico(),
      complemento: "",
      telefones: [formatarTelefone(telefone)],
      callType: null,
      telefoneParaRetorno: formatarTelefone(telefone),
      ...(financiadoData?.idLinkedGroup &&
        financiadoData?.idGrupo && {
          groupId: financiadoData?.idGrupo,
          linkedGroupId: financiadoData?.idLinkedGroup,
        }),
    };

    const ocorrencia = await POST_DATA(
      "Datacob/historicoAdicionar",
      data,
      false,
      true
    );
    if (ocorrencia.success) {
      toast.info("Ocorrência adicionada com sucesso");
      window.location.reload();
    }
    return ocorrencia;
  }

  const handleClose = async (x) => {
    if (x) {
      if ((await historicoAdicionar()) == false) {
        return;
      }
    }
    onClose();
  };

  useEffect(() => {
    setOptionsSelectContracts(
      contratosAtivos.map((x) => {
        return { label: x.numero_Contrato, value: x.numero_Contrato };
      })
    );

    setOptionsContract(
      contratoSelect.map((x) => {
        return { label: x, value: x };
      })
    );
    getIdOcorrencia();
  }, [isOpen]);

  useEffect(() => {
    if (
      selectedCalculatedContract !== null &&
      selectedCalculatedContract !== undefined
    ) {
      getTotalAtualizado();
      // calculaMinEntrada()
    }
  }, [selectedCalculatedContract]);

  useEffect(() => {
    if (
      selectedCalculatedContract == null ||
      selectedCalculatedContract == undefined
    )
      setSelectedCalculatedContract(optionsSelectContracts[0]);
  }, [optionsSelectContracts]);

  useEffect(() => {
    setObjValue({ ...objValue, entrada: minEntrada });
  }, [minEntrada]);

  const handleChangeSelectedCalculatedContract = (item) => {
    setSelectedCalculatedContract(item);
  };

  const handleInputChange = (event) => {
    const input = event.target.value.replace(/\./g, "").replace(/,/g, "");

    let value = 0;

    value = Number(input) / 100;
    if (input.length > 11) return;

    setObjValue({ ...objValue, [event.target.name]: value });
  };

  const handleInputIntChange = (event) => {
    const input = Number(event.target.value.replace(/\D/g, ""));

    setObjValue({ ...objValue, [event.target.name]: input });
  };

  const handleDateInitialInstallmentChange = (date) => {
    setObjValue({ ...objValue, parcelaInicial: date });
  };
  const handleDateFinalInstallmentChange = (date) => {
    setObjValue({ ...objValue, parcelaFinal: date });
  };

  const handleDateChange = (date) => {
    setObjValue({ ...objValue, dataVencimento: date });
  };

  const handleDateEntryChange = (date) => {
    setObjValue({ ...objValue, dataEntrada: date });
  };

  useEffect(() => {
    const val = parseFloat(
      (
        (objValue?.totalAtualizado - objValue?.entrada + objValue?.vincenda) /
        objValue?.qtdParcelas
      ).toFixed(2)
    );
    if (isNaN(val) || !isFinite(val)) setValorParcela(0);
    else setValorParcela(val);
  }, [objValue]);

  const statusTelefoneName = (item) => {
    return item.status === 1
      ? " Ativo "
      : item.status === 2
      ? " Efetivo "
      : item.status === 3
      ? " Pesquisado "
      : item.status === -1
      ? " Blacklist "
      : " Inativo ";
  };
  useEffect(() => {
    const telJson = JSON.parse(localStorage.getItem("clientData"))?.telefones;
    let telefoneEfetivo = [];
    telefoneEfetivo = telJson.filter((x) => x.status === 2);
    if (telefoneEfetivo.length === 0) {
      telefoneEfetivo = telJson.filter((x) => x.status === 1);
    }
    let telefone = "";
    if (telefoneEfetivo.length > 0)
      telefone = telefoneEfetivo[0].ddd + telefoneEfetivo[0].fone;

    if (telJson !== null && telJson !== undefined) {
      const tel = telJson;
      const telOp = tel.map((item) => {
        let label = statusTelefoneName(item) + " - " + item.ddd + item.fone;
        if (item.descricao !== "" && item.descricao != null)
          label += " - " + item.descricao;

        return {
          label: label,
          value: item.ddd + item.fone,
        };
      });
      const telRetOp = [...optionsTelefoneRetorno, ...telOp];

      setOptionsTelefoneRetorno(telRetOp);
      if (telefone !== "") {
        let telRet = telRetOp.find((x) => x.value === telefone);
        if (telRet) {
          setSelectedRetornoTelefone(telRet);
        }
      }
    }
  }, [isOpen]);

  const handleTelefoneRetornoChange = (event) => {
    setSelectedRetornoTelefone(event);
  };

  return (
    <CModal
      show={isOpen}
      onClose={handleClose}
      closeOnBackdrop={false}
      size="lg"
      className="custom-modal"
    >
      {" "}
      <CModalHeader closeButton className="my-0 pt-2 pb-0">
        <h5>Cálculo - Parcelamento:</h5>
      </CModalHeader>
      <CModalBody>
        <CRow>
          <CCol md="4">
            <CLabel>Contrato Selecionado:</CLabel>
          </CCol>
          <CCol md="8">
            <Select
              value={selectedCalculatedContract}
              options={optionsSelectContracts}
              onChange={handleChangeSelectedCalculatedContract}
              placeholder={"Selecione o contrato"}
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor atualizado (Datacob):</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              value={formatCurrency(objValue?.totalAtualizado, false)}
              disabled
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor Mínimo de Entrada:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput value={formatCurrency(minEntrada, false)} disabled />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Contrato:</CLabel>
          </CCol>
          <CCol md="8">
            <Select
              value={selectedContract}
              options={optionsContract}
              onChange={(e) => {
                setSelectedContract(e);
                setObjValue({ ...objValue, contrato: e.value });
              }}
              placeholder={"Selecione"}
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Telefone</CLabel>
          </CCol>
          <CCol md="8">
            <Select
              value={selectedRetornoTelefone}
              onChange={handleTelefoneRetornoChange}
              options={optionsTelefoneRetorno}
              placeholder={"Selecione"}
            ></Select>
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor atualizado:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              value={formatCurrency(objValue?.totalAtualizado, false)}
              onChange={handleInputChange}
              name="totalAtualizado"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Parcelas Negociadas:</CLabel>
          </CCol>
          <CCol md="8">
            <CRow>
              <CCol md="5">
                <ReactDatePicker
                  selected={objValue?.parcelaInicial}
                  onChange={handleDateInitialInstallmentChange}
                  className="form-control"
                  dateFormat="MM/yyyy"
                  showMonthYearPicker
                  locale={ptBR}
                  name="parcelaInicial"
                  placeholderText="Parcela Inicial"
                  onKeyDown={(e) => e.preventDefault()}
                />
              </CCol>{" "}
              <CCol md="1" className={"p-1"}>
                <span>a</span>
              </CCol>
              <CCol md="5">
                <ReactDatePicker
                  selected={objValue?.parcelaFinal}
                  onChange={handleDateFinalInstallmentChange}
                  className="form-control"
                  dateFormat="MM/yyyy"
                  showMonthYearPicker
                  locale={ptBR}
                  name="parcelaFinal"
                  placeholderText="Parcela Final"
                  onKeyDown={(e) => e.preventDefault()}
                />
              </CCol>
            </CRow>
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Valor de Entrada:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              value={formatCurrency(objValue?.entrada, false)}
              onChange={handleInputChange}
              name="entrada"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Vincenda:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              value={formatCurrency(objValue?.vincenda, false)}
              onChange={handleInputChange}
              name="vincenda"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Data de Entrada:</CLabel>
          </CCol>
          <CCol md="8">
            <ReactDatePicker
              selected={objValue?.dataEntrada}
              onChange={handleDateEntryChange}
              className="form-control"
              minDate={today}
              dateFormat="dd/MM/yyyy"
              onKeyDown={(e) => e.preventDefault()}
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Qtd de Parcelas:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput
              onChange={handleInputIntChange}
              value={objValue?.qtdParcelas}
              name="qtdParcelas"
            />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>valor das parcelas:</CLabel>
          </CCol>
          <CCol md="8">
            <CInput value={formatCurrency(valorParcela, false)} disabled />
          </CCol>
        </CRow>
        <br />
        <CRow>
          <CCol md="4">
            <CLabel>Primeiro Vencimento:</CLabel>
          </CCol>
          <CCol md="8">
            <ReactDatePicker
              selected={objValue?.dataVencimento}
              onChange={handleDateChange}
              className="form-control"
              minDate={today}
              dateFormat="dd/MM/yyyy"
              onKeyDown={(e) => e.preventDefault()}
            />
          </CCol>
        </CRow>
      </CModalBody>
      <CModalFooter className={"justify-content-center"}>
        <CButton
          color="success"
          className="mr-2"
          onClick={() => handleClose(true)}
          disabled={objValue?.totalAtualizado <= 0}
        >
          {isLoading ? (
            <>
              <CSpinner size="sm" className="mr-1" />
              Gravar e Gerar Ocorrência
            </>
          ) : (
            "Gravar e Gerar Ocorrência"
          )}
        </CButton>
        <CButton
          color="danger"
          className="mr-2"
          onClick={() => handleClose(false)}
        >
          Fechar
        </CButton>
      </CModalFooter>
    </CModal>
  );
};

export default CalcTipoUmRNI;
