import {
  <PERSON>utton,
  CModal,
  CModalBody,
  CModalHeader,
  CModalFooter,
} from "@coreui/react";
import { useState } from "react";
import { toast } from "react-toastify";
import { deleteApiOnBody } from "src/reusable/functions";
import LoadingComponent from "src/reusable/Loading";

const CancelarPixModal = ({ isOpen, onClose, item, getDataPix }) => {
  const [loading, setLoading] = useState(false);
  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : null;

  const handleCancelarPix = async () => {
    setLoading(true);
    try {
      const response = await deleteApiOnBody(
        {
          id: item.idPix,
          idMotivo: 20,
          crm: financiadoData?.coddatacob,
        },
        "deleteCancelarPix"
      );
      if (response.success) {
        toast.success("Pix cancelado com sucesso!");
        getDataPix();
        onClose();
      } else {
        toast.error(response.message);
      }
    } catch (error) {
      console.log(error);
    }
    setLoading(false);
  };

  return (
    <CModal show={isOpen} size="lg" onClose={onClose} closeOnBackdrop={false}>
      <CModalHeader closeButton>Cancelar PIX</CModalHeader>
      <CModalBody>
        <div>
          <p>
            Você tem certeza que deseja cancelar o PIX? Isso não poderá ser
            desfeito.
          </p>
        </div>
      </CModalBody>
      <CModalFooter className="justify-content-center">
        {!loading && (
          <>
            <CButton color="success" onClick={handleCancelarPix}>
              Sim
            </CButton>
            <CButton color="danger" onClick={onClose}>
              Não
            </CButton>
          </>
        )}
        {loading && <LoadingComponent />}
      </CModalFooter>
    </CModal>
  );
};

export default CancelarPixModal;
