import React, { use<PERSON><PERSON>back, useEffect, useState } from "react";

import {
  CButton,
  CCard,
  CCardBody,
  CCol,
  CInput,
  CModal,
  CModalBody,
  CModalFooter,
  CModalHeader,
  CRow,
  CTextarea,
} from "@coreui/react";
import {
  getApi,
  putApi,
  postApi,
  getApiInline,
  deleteApi,
  deleteApiInline,
} from "src/reusable/functions";
import Select from "react-select";

import "react-quill/dist/quill.snow.css";

import { Editor } from "@tinymce/tinymce-react";
import LoadingComponent from "src/reusable/Loading";
import { toast } from "react-toastify";
import { getURI } from "src/config/apiConfig";

// Função auxiliar para buscar dados
const GetData = async (payload: any, endpoint: string = "") => {
  try {
    const response = await getApi(payload, endpoint);
    return response;
  } catch (error) {
    throw error;
  }
};

interface TipoCartaDiluicao {
  id: string;
  nome: string;
  descricao: string;
  ativo: boolean;
  conteudo: ConteudoCartaDiluicaoType[];
}

interface ConteudoCartaDiluicaoType {
  nome: string;
  crm: string;
  grupoId: Number | null;
}

interface Props {
  isOpen: boolean;
  onClose: () => void;
}

const ConteudoCartaDiluicaosModal = ({ isOpen, onClose }: Props) => {
  const [tiposCartaDiluicao, setTiposCartaDiluicaos] =
    useState<TipoCartaDiluicao | null>(null);

  const [selectedCartaDiluicao, setSelectedCartaDiluicao] =
    useState<TipoCartaDiluicao | null>(null);
  const [selectedConteudoNome, setSelectedConteudoNome] = useState<
    string | null
  >(null);
  const [editingCartaDiluicao, setEditingCartaDiluicao] =
    useState<TipoCartaDiluicao | null>(null);
  const [cabecalhoFile, setCabecalhoFile] = useState<File | null>(null);
  const [rodapeFile, setRodapeFile] = useState<File | null>(null);
  const [cabecalhoPreview, setCabecalhoPreview] = useState("");
  const [rodapePreview, setRodapePreview] = useState("");
  const [expandedCartaDiluicaos, setExpandedCartaDiluicaos] = useState<
    Set<string>
  >(new Set());

  const [loading, setLoading] = useState(false);
  const [loadingConteudo, setLoadingConteudo] = useState(false);
  const [conteudoCartaDiluicaoSelected, setConteudoCartaDiluicaoSelected] =
    useState(null);

  const [value, setValue] = useState("");

  // Estados para inserção de novo layout
  const [showInsertModal, setShowInsertModal] = useState(false);
  const [selectedCartaDiluicaoForInsert, setSelectedCartaDiluicaoForInsert] =
    useState<TipoCartaDiluicao | null>(null);
  const [insertLoading, setInsertLoading] = useState(false);

  // Estados para CRM e Grupos
  const [crmsOptions, setCrmsOptions] = useState<any[]>([]);
  const [gruposOptions, setGruposOptions] = useState<any[]>([]);
  const [crmSelected, setCrmSelected] = useState<any>(null);
  const [grupoSelected, setGrupoSelected] = useState<any>(null);

  const handleSelectCartaDiluicao = (
    carta: TipoCartaDiluicao,
    conteudo: ConteudoCartaDiluicaoType | null
  ) => {
    setSelectedConteudoNome(conteudo?.nome || null);
    setSelectedCartaDiluicao(carta);
    setEditingCartaDiluicao({ ...carta });
    getConteudoCartaDiluicaoByTipeCartaDiluicao(carta.id).then((response) => {
      const content = response.find(
        (x: { grupoId: Number | null; crm: string | null }) =>
          x.grupoId === (conteudo?.grupoId ?? null) &&
          x.crm === (conteudo?.crm ?? null)
      );
      setValue(content?.html);
      setConteudoCartaDiluicaoSelected(content);
      setCabecalhoFile(null);
      setRodapeFile(null);
      setCabecalhoPreview("");
      setRodapePreview("");
    });
  };

  const toggleExpandCartaDiluicao = (termoId: string) => {
    setExpandedCartaDiluicaos((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(termoId)) {
        newSet.delete(termoId);
      } else {
        newSet.add(termoId);
      }
      return newSet;
    });
  };

  const handleCabecalhoUpload = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (file) {
      setCabecalhoFile(file);
      // Criar preview da imagem
      const reader = new FileReader();
      reader.onload = (e) => {
        setCabecalhoPreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleRodapeUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setRodapeFile(file);
      // Criar preview da imagem
      const reader = new FileReader();
      reader.onload = (e) => {
        setRodapePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const openImageInNewTab = (imageData: string, title: string) => {
    const newWindow = window.open();
    if (newWindow) {
      newWindow.document.title = title;
      newWindow.document.body.style.margin = "0";
      newWindow.document.body.style.display = "flex";
      newWindow.document.body.style.justifyContent = "center";
      newWindow.document.body.style.alignItems = "center";
      newWindow.document.body.style.minHeight = "100vh";
      newWindow.document.body.style.background = "#f5f5f5";

      const img = newWindow.document.createElement("img");
      img.src = imageData;
      img.style.maxWidth = "100%";
      img.style.maxHeight = "100%";
      img.style.objectFit = "contain";
      img.alt = title;

      newWindow.document.body.appendChild(img);
    }
  };

  // Funções para buscar CRMs e grupos
  const getCrms = useCallback(() => {
    return GetData({}, "getCrms")
      .then((res: any) => {
        if (res) return res;
      })
      .catch((err) => {
        console.warn(err);
        return null;
      });
  }, []);

  const getGrupoCrm = useCallback((crm: string) => {
    return GetData({ ActiveConnection: crm }, "getGrupoDataCobLista")
      .then((res: any) => {
        if (res && res !== null && res !== undefined) {
          return res;
        }
      })
      .catch((err) => {
        console.warn(err);
        return null;
      });
  }, []);

  const fetchGroupsForCrm = useCallback(
    (crm: any) => {
      return getGrupoCrm(crm.datacobName).then((groupData) => {
        crm.grupos = groupData;
        return crm;
      });
    },
    [getGrupoCrm]
  );

  const handleCrmsChange = (selectedCrm: any) => {
    setCrmSelected(selectedCrm);
    setGrupoSelected(null);
    if (selectedCrm && selectedCrm.grupos) {
      setGruposOptions(selectedCrm.grupos);
    } else {
      setGruposOptions([]);
    }
  };

  const handleGrupoChange = (selectedGrupo: any) => {
    setGrupoSelected(selectedGrupo);
  };

  const handleOpenInsertModal = (termo: TipoCartaDiluicao) => {
    setSelectedCartaDiluicaoForInsert(termo);
    setCrmSelected(null);
    setGrupoSelected(null);
    setGruposOptions([]);
    setShowInsertModal(true);

    // Carregar CRMs quando abrir o modal
    getCrms().then((crms) => {
      if (crms) {
        Promise.all(crms.map(fetchGroupsForCrm)).then((crmGroupData) => {
          setCrmsOptions(crmGroupData);
        });
      }
    });
  };

  const handleCloseInsertModal = () => {
    setShowInsertModal(false);
    setSelectedCartaDiluicaoForInsert(null);
    setCrmSelected(null);
    setGrupoSelected(null);
    setGruposOptions([]);
  };

  const handleInsertLayout = async () => {
    if (!selectedCartaDiluicaoForInsert || !crmSelected || !grupoSelected) {
      toast.warning(
        "Por favor, preencha todos os campos obrigatórios (CRM e Grupo)."
      );
      return;
    }

    try {
      setInsertLoading(true);

      const payload = {
        tipoCartaDiluicaoId: selectedCartaDiluicaoForInsert.id,
        crm: crmSelected.datacobName || null,
        grupoId: grupoSelected.id_Grupo || null,
      };

      // Chamada para a API de inserção
      const response = await postApi(
        payload,
        "postConteudoCartaDiluicaoFromDefault"
      );

      if (response?.success !== true) {
        throw new Error(response?.message || "Erro ao inserir layout");
      }

      // Recarregar os tipos de termo para mostrar o novo layout
      await getTipoCartaDiluicao();

      handleCloseInsertModal();
      toast.success("Layout inserido com sucesso!");
    } catch (error) {
      console.error("Erro ao inserir layout:", error);
      toast.error("Erro ao inserir layout. Tente novamente.");
    } finally {
      setInsertLoading(false);
    }
  };

  const handleSave = async () => {
    if (!editingCartaDiluicao) return;
    toast.info("Salvando conteúdo do termo...");
    setSelectedCartaDiluicao(editingCartaDiluicao);
    if (await updateConteudoCartaDiluicao())
      toast.success("Conteúdo do termo atualizado com sucesso!");
    else toast.error("Erro ao atualizar conteúdo do termo!");
  };

  const asyncLoadFunc = useCallback(async () => {
    await Promise.all([getTipoCartaDiluicao()]);
  }, []);

  const getTipoCartaDiluicao = async () => {
    setLoading(true);
    try {
      const response = await getApi({}, "getTipoDefaultCartaDiluicao");
      if (response) {
        setTiposCartaDiluicaos(response);
      } else {
        setTiposCartaDiluicaos(null);
      }
    } catch (error) {
      toast.error("Erro ao carregar tipo de carta diluicao");
    }
    setLoading(false);
  };

  const getConteudoCartaDiluicaoByTipeCartaDiluicao = async (id: string) => {
    try {
      setLoadingConteudo(true);
      const response = await getApiInline(id, "getConteudoCartaDiluicaoByTipo");
      setLoadingConteudo(false);
      return response;
    } catch (error) {
      console.error(error);
      setLoadingConteudo(false);
      return [];
    }
  };

  const updateConteudoCartaDiluicao = async () => {
    if (!conteudoCartaDiluicaoSelected) return;
    const formData = new FormData();
    formData.append("id", conteudoCartaDiluicaoSelected?.id);
    formData.append("html", value);
    // Adicionar arquivos se foram selecionados, senão manter os existentes como base64
    if (cabecalhoFile) {
      formData.append("cabecalhoImg", cabecalhoFile);
    }

    if (rodapeFile) {
      formData.append("rodapeImg", rodapeFile);
    }
    formData.append("grupoId", conteudoCartaDiluicaoSelected?.grupoId || "");
    formData.append("crm", conteudoCartaDiluicaoSelected?.crm ?? "");

    const result = await fetch(getURI("putConteudoCartaDiluicao"), {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${window.localStorage.getItem("token")}`,
      },
      body: formData,
    });
    const res = await result.json();

    if (res?.status === 400) {
      return false;
    }
    if (res?.success === false) {
      return false;
    }
    return true;
  };

  useEffect(() => {
    asyncLoadFunc();
    return () => {};
  }, [asyncLoadFunc]);

  const [layoutToDelete, setLayoutToDelete] =
    useState<ConteudoCartaDiluicaoType | null>(null);
  const [loadingDelete, setLoadingDelete] = useState(false);
  const deleteConteudoCartaDiluicao = async (id: string) => {
    try {
      setLoadingDelete(true);
      const response = await deleteApiInline(id, "deleteConteudoCartaDiluicao");
      if (response?.success === true) {
        toast.success("Conteúdo excluído com sucesso!");
      } else {
        toast.error("Erro ao excluir conteúdo!");
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao excluir conteúdo!");
    } finally {
      setLoadingDelete(false);
    }
  };
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const handleCloseDeleteModal = () => {
    setShowDeleteModal(false);
    setLayoutToDelete(null);
  };

  const handleDeleteModal = (content: ConteudoCartaDiluicaoType) => {
    if (tiposCartaDiluicao.id) {
      setLayoutToDelete(content);
      setShowDeleteModal(true);
    }
  };

  const handleDeleteLayout = async () => {
    if (!layoutToDelete) return;
    if (!tiposCartaDiluicao.id) return;
    if (!tiposCartaDiluicao.conteudo) return;
    setLoadingDelete(true);
    try {
      const response = await getConteudoCartaDiluicaoByTipeCartaDiluicao(
        tiposCartaDiluicao.id
      );
      const content = response.find(
        (x: { grupoId: Number | null; crm: string | null }) =>
          x.grupoId === (layoutToDelete?.grupoId ?? null) &&
          x.crm === (layoutToDelete?.crm ?? null)
      );
      if (content) {
        deleteConteudoCartaDiluicao(content.id).then(() => {
          handleCloseInsertModal();
          handleCloseDeleteModal();
          getTipoCartaDiluicao();
        });
      }
    } catch (error) {
      console.error(error);
      toast.error("Erro ao excluir conteúdo!");
      handleCloseDeleteModal();
    }
    setLoadingDelete(false);
  };

  return (
    <>
      <CModal
        show={isOpen}
        onClose={onClose}
        closeOnBackdrop={false}
        size="xl"
        className="custom-modal modal-xxl"
      >
        <CModalHeader closeButton>
          <h5>Gerenciar Tipos de CartaDiluicaos</h5>
        </CModalHeader>

        <CModalBody>
          <CRow>
            {/* Lista de Tipos de CartaDiluicaos */}
            <CCol md="4">
              <CCard>
                <CCardBody>
                  <div className="d-flex justify-content-between align-items-center mb-3">
                    <h6 className="mb-0">Tipos de CartaDiluicaos</h6>
                  </div>
                  {loading ? (
                    <LoadingComponent />
                  ) : (
                    <>
                      {tiposCartaDiluicao && (
                        <div style={{ maxHeight: "500px", overflowY: "auto" }}>
                          <div key={tiposCartaDiluicao.id} className="mb-2">
                            <div
                              className={`p-2 border rounded ${
                                selectedCartaDiluicao?.id ===
                                tiposCartaDiluicao.id
                                  ? "bg-primary text-white"
                                  : "bg-light"
                              }`}
                              style={{
                                cursor:
                                  tiposCartaDiluicao.conteudo &&
                                  tiposCartaDiluicao.conteudo.length > 0
                                    ? "pointer"
                                    : "default",
                              }}
                              onClick={() => {
                                if (
                                  tiposCartaDiluicao.conteudo &&
                                  tiposCartaDiluicao.conteudo.length > 0
                                ) {
                                  toggleExpandCartaDiluicao(
                                    tiposCartaDiluicao.id
                                  );
                                }
                              }}
                            >
                              <div className="d-flex justify-content-between align-items-center">
                                <div className="d-flex align-items-center">
                                  {tiposCartaDiluicao.conteudo &&
                                    tiposCartaDiluicao.conteudo.length > 0 && (
                                      <i
                                        className={`cil-chevron-${
                                          expandedCartaDiluicaos.has(
                                            tiposCartaDiluicao.id
                                          )
                                            ? "bottom"
                                            : "right"
                                        } mr-2`}
                                      />
                                    )}
                                  <strong>{"Carta Diluição Padrão"}</strong>
                                </div>
                                <div className="d-flex align-items-center">
                                  <i
                                    className="cil-plus"
                                    style={{
                                      cursor: "pointer",
                                      marginRight: "8px",
                                      color: "#28a745",
                                      fontWeight: "bold",
                                    }}
                                    title="Adicionar conteúdo ao termo"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleOpenInsertModal(tiposCartaDiluicao);
                                    }}
                                  />
                                  <i
                                    className="cil-pencil"
                                    style={{ cursor: "pointer" }}
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      handleSelectCartaDiluicao(
                                        tiposCartaDiluicao,
                                        null
                                      );
                                    }}
                                  />
                                </div>
                              </div>
                            </div>

                            {expandedCartaDiluicaos.has(
                              tiposCartaDiluicao.id
                            ) && (
                              <div className="ml-4 mt-2">
                                {tiposCartaDiluicao.conteudo.map((c, i) => (
                                  <div
                                    key={i}
                                    className={`p-2 mb-1 border rounded ${
                                      selectedCartaDiluicao?.id ===
                                        tiposCartaDiluicao.id &&
                                      selectedConteudoNome === c.nome
                                        ? "bg-info text-white"
                                        : "bg-white"
                                    }`}
                                    style={{
                                      cursor: "pointer",
                                      marginLeft: "20px",
                                    }}
                                    onClick={() =>
                                      handleSelectCartaDiluicao(
                                        tiposCartaDiluicao,
                                        c
                                      )
                                    }
                                  >
                                    <div className="d-flex justify-content-between align-items-center">
                                      <div>
                                        <strong>
                                          {c.nome} - {c.crm}
                                        </strong>
                                      </div>

                                      <div className="d-flex align-items-center">
                                        <i
                                          className="cil-trash"
                                          style={{
                                            cursor: "pointer",
                                            marginRight: "8px",
                                          }}
                                          title="Excluir layout"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleDeleteModal(c);
                                          }}
                                        />
                                      </div>
                                    </div>
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </>
                  )}
                </CCardBody>
              </CCard>
            </CCol>

            {/* Conteúdo de Edição */}
            <CCol md="8">
              <CCard>
                <CCardBody>
                  {selectedCartaDiluicao ? (
                    <div>
                      {loadingConteudo ? (
                        <LoadingComponent />
                      ) : (
                        <>
                          <h6 className="mb-3">
                            Editar Tipo de Carta Diluição
                          </h6>

                          <div className="row mb-3">
                            {selectedConteudoNome !== null && (
                              <>
                                <div className="col-md-6">
                                  <label htmlFor="nome">CRM:</label>
                                  <CInput
                                    id="nome"
                                    value={
                                      conteudoCartaDiluicaoSelected?.crm || ""
                                    }
                                    disabled={true}
                                    placeholder="Nome do tipo de termo"
                                  />
                                </div>
                                <div className="col-md-6">
                                  <label htmlFor="nome">Grupo:</label>
                                  <CInput
                                    id="nome"
                                    value={selectedConteudoNome || ""}
                                    disabled={true}
                                    placeholder="Nome do tipo de termo"
                                  />
                                </div>
                              </>
                            )}
                          </div>

                          {/* Campos de Upload */}
                          <div className="row mb-3">
                            <div className="col-md-6">
                              <label htmlFor="cabecalho">
                                Cabeçalho (Imagem):
                              </label>
                              <input
                                type="file"
                                id="cabecalho"
                                className="form-control"
                                accept="image/*"
                                onChange={handleCabecalhoUpload}
                              />
                              {(cabecalhoFile ||
                                cabecalhoPreview ||
                                conteudoCartaDiluicaoSelected?.cabecalhoImg) && (
                                <div className="mt-2">
                                  <small
                                    className={
                                      cabecalhoFile
                                        ? "text-success"
                                        : "text-info"
                                    }
                                  >
                                    {cabecalhoFile
                                      ? "✓ Novo arquivo carregado"
                                      : "Arquivo existente"}
                                  </small>
                                  <div className="mt-2">
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-outline-primary me-2"
                                      onClick={() => {
                                        const imageData =
                                          cabecalhoPreview ||
                                          (conteudoCartaDiluicaoSelected?.cabecalhoImg
                                            ? `data:image/jpeg;base64,${conteudoCartaDiluicaoSelected.cabecalhoImg}`
                                            : null);
                                        if (imageData) {
                                          openImageInNewTab(
                                            imageData,
                                            "Cabeçalho - Visualização"
                                          );
                                        }
                                      }}
                                    >
                                      <i className="cil-external-link me-1 font-weight-bold"></i>{" "}
                                      Visualizar Cabeçalho
                                    </button>
                                    {cabecalhoFile && (
                                      <button
                                        type="button"
                                        className="btn btn-sm btn-outline-danger ml-1"
                                        onClick={() => {
                                          setCabecalhoFile(null);
                                          setCabecalhoPreview("");
                                        }}
                                      >
                                        Remover novo arquivo
                                      </button>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="col-md-6">
                              <label htmlFor="rodape">Rodapé (Imagem):</label>
                              <input
                                type="file"
                                id="rodape"
                                className="form-control"
                                accept="image/*"
                                onChange={handleRodapeUpload}
                              />
                              {(rodapeFile ||
                                rodapePreview ||
                                conteudoCartaDiluicaoSelected?.rodapeImg) && (
                                <div className="mt-2">
                                  <small
                                    className={
                                      rodapeFile ? "text-success" : "text-info"
                                    }
                                  >
                                    {rodapeFile
                                      ? "✓ Novo arquivo carregado"
                                      : "Arquivo existente"}
                                  </small>
                                  <div className="mt-2">
                                    <button
                                      type="button"
                                      className="btn btn-sm btn-outline-primary me-2"
                                      onClick={() => {
                                        const imageData =
                                          rodapePreview ||
                                          (conteudoCartaDiluicaoSelected?.rodapeImg
                                            ? `data:image/jpeg;base64,${conteudoCartaDiluicaoSelected.rodapeImg}`
                                            : null);
                                        if (imageData) {
                                          openImageInNewTab(
                                            imageData,
                                            "Rodapé - Visualização"
                                          );
                                        }
                                      }}
                                    >
                                      <i className="cil-external-link me-1 font-weight-bold"></i>{" "}
                                      Visualizar Rodapé
                                    </button>
                                    {rodapeFile && (
                                      <button
                                        type="button"
                                        className="btn btn-sm btn-outline-danger"
                                        onClick={() => {
                                          setRodapeFile(null);
                                          setRodapePreview("");
                                        }}
                                      >
                                        Remover novo arquivo
                                      </button>
                                    )}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="mb-3">
                            <p>Tags disponiveis:</p>
                            <div className="row">
                              <div className="col">
                                <p>
                                  <ul className="text-primary">
                                    <li>{"{ClientePrincipal}"}</li>
                                    <li>{"{AdversoPrincipal}"}</li>
                                    <li>{"{GrupoCotaContrato}"}</li>
                                  </ul>
                                </p>
                              </div>
                              <div className="col">
                                <p>
                                  <ul className="text-primary">
                                    <li>{"{MesesParcelas}"}</li>
                                    <li>{"{DataRetornoPagamento}"}</li>
                                    <li>{"{DataBase}"}</li>
                                  </ul>
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="row mb-3">
                            <div className="col-md-12">
                              <label htmlFor="descricao">Descrição:</label>

                              <style>{`.tox-promotion { display: none !important; }`}</style>

                              <Editor
                                apiKey="0tikha1mpfegov516ubzkw3x7y9rm53pexmcj1if4s11cjcx"
                                value={value}
                                onEditorChange={(newValue: string) =>
                                  setValue(newValue)
                                }
                                init={{
                                  height: 300,
                                  menubar: true,
                                  plugins: [
                                    "table",
                                    "lists",
                                    "link",
                                    "image",
                                    "code",
                                    "advlist",
                                    "autolink",
                                    "fullscreen",
                                  ],
                                  toolbar:
                                    "undo redo | blocks | bold italic underline | alignleft aligncenter alignright | bullist numlist | table | link image | code | fullscreen",
                                  branding: false,
                                }}
                              />
                            </div>
                          </div>

                          <div className="d-flex justify-content-between">
                            <CButton color="primary" onClick={handleSave}>
                              Salvar
                            </CButton>
                          </div>
                        </>
                      )}
                    </div>
                  ) : (
                    <div className="text-center text-muted">
                      <h6>Selecione um tipo de termo para editar</h6>
                      <p>
                        Escolha um item da lista ao lado para visualizar e
                        editar suas informações.
                      </p>
                    </div>
                  )}
                </CCardBody>
              </CCard>
            </CCol>
          </CRow>
        </CModalBody>

        <CModalFooter>
          <CButton color="secondary" onClick={onClose}>
            Fechar
          </CButton>
        </CModalFooter>
      </CModal>

      {/* Modal de Inserção de Layout */}
      <CModal
        show={showInsertModal}
        onClose={handleCloseInsertModal}
        closeOnBackdrop={false}
        size="lg"
        className="custom-modal"
      >
        <CModalHeader closeButton>
          <h5>Adicionar Layout para {selectedCartaDiluicaoForInsert?.nome}</h5>
        </CModalHeader>

        <CModalBody>
          <div className="row mb-3">
            <div className="col-md-6">
              <label>CRM: *</label>
              <Select
                options={crmsOptions}
                value={crmSelected ?? null}
                onChange={handleCrmsChange}
                getOptionValue={(option) => option.id}
                getOptionLabel={(option) => option.datacobName}
                placeholder="Selecione o CRM"
                isClearable
              />
            </div>
            <div className="col-md-6">
              <label>Grupo: *</label>
              <Select
                options={gruposOptions}
                value={grupoSelected ?? null}
                onChange={handleGrupoChange}
                getOptionValue={(option) => option.id_Grupo}
                getOptionLabel={(option) => option.descricao}
                placeholder="Selecione o Grupo"
                isDisabled={!crmSelected}
                isClearable
              />
            </div>
          </div>

          <div className="alert alert-info">
            <strong>Informação:</strong> O layout será criado com conteúdo
            padrão. Após a criação, você poderá editá-lo clicando no ícone de
            lápis.
          </div>
        </CModalBody>

        <CModalFooter>
          <CButton
            color="success"
            onClick={handleInsertLayout}
            disabled={insertLoading || !crmSelected || !grupoSelected}
          >
            {insertLoading ? "Inserindo..." : "Inserir Layout"}
          </CButton>
          <CButton color="secondary" onClick={handleCloseInsertModal}>
            Cancelar
          </CButton>
        </CModalFooter>
      </CModal>

      {/* Modal de Exclusão */}
      <CModal
        show={showDeleteModal}
        onClose={handleCloseDeleteModal}
        closeOnBackdrop={false}
        size="sm"
        className="custom-modal"
      >
        <CModalHeader closeButton>
          <h5>Excluir Layout</h5>
        </CModalHeader>
        <CModalBody>
          Tem certeza que deseja excluir o layout selecionado?
        </CModalBody>
        <CModalFooter>
          <CButton color="danger" onClick={handleCloseDeleteModal}>
            Cancelar
          </CButton>
          <CButton
            color="success"
            onClick={handleDeleteLayout}
            disabled={loadingDelete}
          >
            {loadingDelete ? "Excluindo..." : "Confirmar"}
          </CButton>
        </CModalFooter>
      </CModal>
    </>
  );
};

export default ConteudoCartaDiluicaosModal;
