import React, { useState, useEffect } from "react";
import Select from "react-select";
import {
  CCol,
  CRow,
  CCard,
  CCardBody,
  CButton,
  CTabs,
  CNav,
  CNavItem,
  CNavLink,
  CTabContent,
  CTabPane,
  CTooltip,
} from "@coreui/react";
import { GET_DATA } from "src/api";
import { formatDateTime } from "src/reusable/helpers";
import CreateOcorrenciaModal from "../ocorrencias/CreateOcorrenciaModal";
import { useAuth } from "src/auth/AuthContext";
import NaoHaDadosTables from "src/reusable/NaoHaDadosTables";
import _ from "lodash";
import TableSelectItens from "src/reusable/TableSelectItens";
import CardLoading from "src/reusable/CardLoading";
import { useMyContext } from "src/reusable/DataContext";
import OcorrenciasAutomacao from "./AcompanhamentoAutomacao";
import { getURI } from "src/config/apiConfig";
import { useWebsocketTelefoniaContext } from "src/websocketProvider/websocketTelefoniaProvider";
import ObsMoreModal from "../telaPrincipal/Modal/ObsMoreModal.tsx";

const Ocorrencias = () => {
  const { data } = useMyContext();
  const { checkPermission, inforPermissions } = useAuth();

  const { message } = useWebsocketTelefoniaContext();
  const permissao = {
    modulo: "Histórico e registros",
    submodulo: "Adicionar ocorrências",
  };

  const [financiadoData, setFinanciadoData] = useState(
    localStorage.getItem("financiadoData")
      ? JSON.parse(localStorage.getItem("financiadoData"))
      : null
  );

  const callData = message;

  const [dadosHistoricos, setDadosHistoricos] = useState(null);

  const [selectedOcorrencia, setSelectedOcorrencia] = useState("");
  const [selectedCodigo, setSelectedCodigo] = useState("");
  const [selectedUsuario, setSelectedUsuario] = useState("");
  const [ocorrenciaOptions, setOcorrenciaOptions] = useState([]);
  const [codigoOptions, setCodigoOptions] = useState([]);
  const [usuarioOptions, setUsuarioOptions] = useState([]);
  const [filteredData, setFilteredeData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [modalShow, setModalShow] = useState(false);
  const [checkedSms, setCheckedSms] = useState(false);

  const [obsModalShow, setObsModalShow] = useState(false);
  const [obsModal, setObsModal] = useState("");

  const [isRpa, setIsRpa] = useState(false);

  async function getRpaConfig() {
    const response = await GET_DATA(
      getURI("getConfigByKey"),
      null,
      true,
      true,
      "rpa_save_occurrences_active"
    );
    if (response == "1") {
      setIsRpa(true);
    }
  }

  const handleOcorrenciaChange = (selectedOption) => {
    setSelectedOcorrencia(selectedOption.value);
  };

  const handleCodigoChange = (selectedOption) => {
    setSelectedCodigo(selectedOption.value);
  };

  const handleUsuarioChange = (selectedOption) => {
    setSelectedUsuario(selectedOption.value);
  };

  async function getHistoricoAtendimento(
    Id_Agrupamento,
    numeroContrato,
    groupId,
    linkedGroupId
  ) {
    const data = {
      Id_Agrupamento: Id_Agrupamento,
      numeroContrato: numeroContrato,
      ...(linkedGroupId &&
        groupId && {
          groupId: groupId,
          linkedGroupId: linkedGroupId,
        }),
    };
    const historicoAtendimento = await GET_DATA(
      "Datacob/HistoricoAtendimento",
      data
    );

    if (historicoAtendimento) {
      const filteredHistorico = historicoAtendimento;
      return filteredHistorico;
    } else return null;
  }

  const handleCheckedSms = () => {
    setCheckedSms(!checkedSms);
  };

  async function updateTable() {
    setIsLoading(true);
    const novoHistorico = await getHistoricoAtendimento(
      financiadoData?.id_Agrupamento,
      financiadoData?.numero_Contrato,
      financiadoData?.id_Grupo,
      financiadoData?.idLinkedGroup
    );
    if (
      novoHistorico !== null &&
      novoHistorico !== undefined &&
      novoHistorico !== false &&
      financiadoData !== null
    ) {
      setDadosHistoricos(novoHistorico);
      setFilteredeData(
        novoHistorico.filter(
          (item) =>
            item.cod_Ocorr_Sistema !== "030" && //SMS Enviado
            item.cod_Ocorr_Sistema !== "104" && //SMS Com Erro
            item.cod_Ocorr_Sistema !== "105" && //SMS Com Sucesso
            item.cod_Ocorr_Sistema !== "182" //Digital | Negociação SMS
        )
      );

      const uniqueOcorrencias = [
        ...new Set(novoHistorico.map((item) => item.descricao)),
      ];
      const uniqueCodigos = [
        ...new Set(novoHistorico.map((item) => item.cod_Ocorr_Sistema)),
      ];
      const uniqueUsuarios = [
        ...new Set(novoHistorico.map((item) => item.nome)),
      ];

      const optionsOcorrencias = [
        { value: "", label: "Ocorrência" }, // "All" option
        ...uniqueOcorrencias.map((Ocorrencia) => ({
          value: Ocorrencia,
          label: Ocorrencia,
        })),
      ];

      const optionsCodigo = [
        { value: "", label: "Código" },
        ...uniqueCodigos.map((Cod) => ({
          value: Cod,
          label: Cod,
        })),
      ];

      const optionsUsuarios = [
        { value: "", label: "Usuário" },
        ...uniqueUsuarios.map((User) => ({
          value: User,
          label: User,
        })),
      ];

      setOcorrenciaOptions(optionsOcorrencias);
      setCodigoOptions(optionsCodigo);
      setUsuarioOptions(optionsUsuarios);
    }

    setIsLoading(false);
  }

  const handleSave = async () => {
    await updateTable();
  };

  const handleClose = () => {
    setModalShow(false);
  };

  const tableColumns = [
    {
      key: "dt_ocorr",
      label: "Datas",
      formatter: (value) => formatDateTime(value),
      defaultSort: "descending",
    },
    { key: "cod_Ocorr_Sistema", label: "Código" },
    {
      key: "descricao",
      label: "Desc. Ocorrência",
      _style: { whiteSpace: "nowrap" },
    },
    {
      key: "observacao",
      label: "Observações",
      formatter: (item) => renderObsCell(item),
    },
    { key: "complemento", label: "Complemento" },
    { key: "nome", label: "Usuário" },
    {
      key: "callType",
      label: "Tipo de Ligação",
      formatter: (item) =>
        item === "" || item === null || item === undefined ? "---" : item,
    },
    { key: "descricao_Complemento", label: "Detalhes" },
    {
      key: "dddFone",
      label: "Telefone",
      formatter: (item) => renderCell(item),
    },
    {
      key: "dddFoneRetorno",
      label: "Telefone Agendado",
      _style: { whiteSpace: "nowrap" },
    },
  ];

  const renderCell = (item) => {
    return <div style={{ whiteSpace: "nowrap" }}>{item}</div>;
  };

  const renderObsCell = (item) => {
    return (
      <>
        <div className="d-flex justify-content-between">
          <div
            style={{ textWrap: "nowrap" }}
            dangerouslySetInnerHTML={{
              __html: item.replaceAll(/\n/g, " ").slice(0, 40),
            }}
          />
          {item?.replaceAll(/\n/g, " ")?.length > 41 && (
            <CTooltip content={`Ver ocorrência fechada`} placement={"top"}>
              <CButton
                color="info"
                className={" ml-2"}
                style={{ width: "40px", height: "30px" }}
                onClick={() => {
                  setObsModal(item);
                  setObsModalShow(true);
                }}
              >
                <strong>
                  <i className="cil-plus" />
                </strong>
              </CButton>
            </CTooltip>
          )}
        </div>
      </>
    );
  };

  useEffect(() => {
    if (dadosHistoricos) {
      const filteredDataVar = dadosHistoricos.filter((item) => {
        const matchesOcorrencia =
          !selectedOcorrencia || item.descricao === selectedOcorrencia;

        const matchesCodigo =
          !selectedCodigo ||
          item.cod_Ocorr_Sistema === selectedCodigo ||
          (selectedCodigo === "All" && item.cod_Ocorr_Sistema);

        const matchesUsuario =
          !selectedUsuario ||
          item.nome === selectedUsuario ||
          (selectedUsuario === "All" && item.nome);

        let sistemico = true;
        if (!checkedSms) {
          sistemico =
            item.cod_Ocorr_Sistema !== "030" && //SMS Enviado
            item.cod_Ocorr_Sistema !== "104" && //SMS Com Erro
            item.cod_Ocorr_Sistema !== "105" && //SMS Com Sucesso
            item.cod_Ocorr_Sistema !== "182"; //Digital | Negociação SMS
        }
        return (
          matchesOcorrencia && matchesCodigo && matchesUsuario && sistemico
        );
      });

      setFilteredeData(filteredDataVar);
    }
  }, [selectedCodigo, selectedOcorrencia, selectedUsuario, checkedSms]);

  useEffect(() => {
    if (financiadoData && financiadoData?.id_Agrupamento) {
      loadPageAsync();
    }
    getRpaConfig();
  }, []);

  useEffect(() => {
    if (data) {
      setFinanciadoData(data);
    }
  }, [data]);

  useEffect(() => {
    if (financiadoData && financiadoData?.id_Agrupamento) {
      loadPageAsync();
    }
  }, [financiadoData]);

  const loadPageAsync = async () => {
    await updateTable();
    if (
      // callData?.callId &&
      callData?.status === "Wrap" ||
      callData?.status === "WrapWithEnding" ||
      callData?.status === "WrapWithManualCall" ||
      callData?.status === "WrapWithPrivateCallback" ||
      callData?.status === "Closed" ||
      callData?.status === "WrapWithPause"
    ) {
      setModalShow(true);
    }
  };

  let tabs = [
    {
      id: 1,
      label: "Histórico e registros",
      icon: "cil-money",
      content: isLoading ? (
        <CardLoading />
      ) : (
        <>
          <CCardBody>
            <CRow>
              <CCol md="2">
                <Select
                  size="sm"
                  options={ocorrenciaOptions}
                  value={ocorrenciaOptions.find(
                    (option) => option.value === selectedOcorrencia
                  )}
                  onChange={handleOcorrenciaChange}
                  placeholder={"Selecione"}
                />
              </CCol>
              <CCol md="2">
                <Select
                  options={codigoOptions}
                  value={codigoOptions.find(
                    (option) => option.value === selectedCodigo
                  )}
                  onChange={handleCodigoChange}
                  placeholder={"Selecione"}
                />
              </CCol>
              <CCol md="2">
                <Select
                  options={usuarioOptions}
                  value={usuarioOptions.find(
                    (option) => option.value === selectedUsuario
                  )}
                  onChange={handleUsuarioChange}
                  placeholder={"Selecione"}
                />
              </CCol>
              <CCol md="2">
                <div className={"form-check form-switch"}>
                  <input
                    checked={checkedSms}
                    className={"form-check-input"}
                    type="checkbox"
                    role="switch"
                    id="flexSwitchCheckDefault"
                    onChange={handleCheckedSms}
                  />
                  <label
                    className={"form-check-label"}
                    htmlFor="flexSwitchCheckDefault"
                  >
                    Sístemico
                  </label>
                </div>
              </CCol>
              <CCol className="d-flex justify-content-end" md="4">
                <CButton
                  to="/telaprincipal"
                  className="btn btn-warning text-white mx-2"
                >
                  Ir para Tela Principal
                </CButton>
                <CButton
                  color="info"
                  onClick={() => setModalShow(true)}
                  title={inforPermissions(permissao).create}
                  disabled={
                    !checkPermission(
                      permissao.modulo,
                      "Create",
                      permissao.submodulo
                    )
                  }
                >
                  <i className="cil-plus" /> Adicionar ocorrência
                </CButton>
              </CCol>
            </CRow>
            {filteredData && filteredData?.length > 0 ? (
              <CRow className="my-2">
                <TableSelectItens
                  data={filteredData}
                  columns={tableColumns}
                  onSelectionChange={(_) => {}}
                  defaultSelectedKeys={[]}
                  selectable={false}
                  heightParam="520px"
                />
              </CRow>
            ) : (
              <NaoHaDadosTables />
            )}
          </CCardBody>
        </>
      ),
    },
  ];
  if (isRpa) {
    tabs.push({
      id: 2,
      label: "Acompanhamento da Automação",
      icon: "cil-media-play",
      content: (
        <>
          <CCardBody>
            <OcorrenciasAutomacao />
          </CCardBody>
        </>
      ),
    });
  }

  const [currentTab, setCurrentTab] = useState("Histórico e registros");
  const handleTabSelect = (tab) => {
    setCurrentTab(tab.label);
    if (tab.label === "Histórico e registros") {
      updateTable();
    }
    if (tab.label === "Acompanhamento da Automação") {
    }
  };

  return (
    <div>
      <CRow className="mx-1 mb-2">
        <h1>Histórico e registros</h1>
      </CRow>
      <CCard>
        <CTabs onSelect={handleTabSelect} activeTab={"Histórico e registros"}>
          <CNav className="custom-nav">
            {tabs.map((tab) => (
              <CNavItem
                key={tab.id}
                className={currentTab === tab.label ? "" : "nonactive-tab"}
              >
                <CNavLink
                  data-tab={tab.label}
                  onClick={() => handleTabSelect(tab)}
                >
                  <i className={tab.icon} /> {tab.label}
                </CNavLink>
              </CNavItem>
            ))}
          </CNav>
          <CTabContent
            className="px-3 overflow-auto"
            style={{ minHeight: "450px" }}
          >
            {tabs.map((tab) => (
              <CTabPane key={tab.id} data-tab={tab.label}>
                {tab.content}
              </CTabPane>
            ))}
          </CTabContent>
        </CTabs>
      </CCard>
      {modalShow && (
        <CreateOcorrenciaModal
          isOpen={modalShow}
          onClose={handleClose}
          onSave={handleSave}
          isRpa={isRpa}
        />
      )}
      {obsModalShow && (
        <ObsMoreModal
          isOpen={obsModalShow}
          onClose={() => setObsModalShow(false)}
          obs={obsModal}
        />
      )}
    </div>
  );
};

export default Ocorrencias;
