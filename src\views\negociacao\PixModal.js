import React, { useState, useEffect, useRef } from "react";
import {
  CModal,
  CModalBody,
  CModalHeader,
  CButton,
  CForm,
  CFormGroup,
  CLabel,
  CInput,
  CCard,
  CRow,
  CCol,
  CCardBody,
  CCardHeader,
} from "@coreui/react";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

import Select from "react-select";
import { GET_DATA, POST_DATA } from "src/api";
import { getURI } from "src/config/apiConfig";
import { formatDate, formatThousands } from "src/reusable/helpers";

import CardLoading from "src/reusable/CardLoading";
import LoadingComponent from "src/reusable/Loading";
import DadosContatoModal from "./Parcial/DadosContatoModal";
import { QRCodeSVG } from "qrcode.react";
import { postApi, putApi } from "src/reusable/functions";

const PixModal = ({ isOpen, onClose, dados, nrParcelas }) => {
  const user = localStorage.getItem("user")
    ? JSON.parse(localStorage.getItem("user"))
    : "";

  const clientData = localStorage.getItem("clientData")
    ? JSON.parse(localStorage.getItem("clientData"))
    : "";

  const financiadoData = localStorage.getItem("financiadoData")
    ? JSON.parse(localStorage.getItem("financiadoData"))
    : "";

  const [pixPayload, setPixPayload] = useState(null);
  const [email, setEmail] = useState("");
  const [selectedEmail, setSelectedEmail] = useState(null);
  const [emailList, setEmailList] = useState([]);

  const qrCodeRef = useRef(null);

  const [selectedNegociador, setSelectedNegociador] = useState(null);
  const [negociadorOptions, setNegociadorOptions] = useState([]);

  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const [checkbuttonToSend, setCheckbuttonToSend] = useState(false);

  const [contatoModal, setContatoModal] = useState(false);

  const handleEmailChange = (target) => {
    setSelectedEmail(target);
    setEmail(target.label);
  };

  const handleInputChange = (event) => {
    setEmail(event.target.value);
  };

  const PixPost = async (boletoData) => {
    let formaDesconto = boletoData.formaDesconto;
    if (
      financiadoData.coddatacob === "Rodobens" &&
      financiadoData.id_Grupo === 3
    ) {
      formaDesconto = 2;
    }
    const payload = {
      ...boletoData,
      email: email,
      formaDesconto: formaDesconto,
      crm: financiadoData.coddatacob,
      IdCobradorResponsavel: selectedNegociador.value,
    };
    const result = await POST_DATA("Datacob/Negociacoes/Pix/Gerar", payload);
    return result;
  };

  const handleSubmit = async (event) => {
    event.preventDefault();
    setIsLoading(true);

    try {
      const result = await PixPost(dados);
      if (result.success) {
        toast.success("Pix gerado com sucesso!");
        setPixPayload(result.data.copiaCola);
        setIsLoading(false);
        return;
      }
      toast.warning(result.message);
    } catch (error) {
      console.error("Ocorreu um erro ao gerar Pix:", error);
      toast.warn(error);
    }
    setIsLoading(false);
  };

  const handleClose = () => {
    onClose();
  };

  const handleNegociadorChange = (target) => {
    setSelectedNegociador(target);
  };

  const getUsers = async () => {
    setIsLoadingUsers(true);
    const currentUser = user;
    const payload = { ActiveConnection: user.activeConnection };
    getData(payload, "getDatacobUsers")
      .then((data) => {
        if (data) {
          const options = data.map((x) => {
            return {
              label: x.nome,
              value: x.id_Usuario,
            };
          });
          setNegociadorOptions(options);
          const findUser = data.find((user) => currentUser.name === user.nome);
          if (findUser) {
            const negociador = {
              label: findUser.nome,
              value: findUser.id_Usuario,
            };
            setSelectedNegociador(negociador);
          }
        }
      })
      .catch((err) => {
        console.log(err);
      });
    setIsLoadingUsers(false);
  };

  const getData = async (payload, endpoint) => {
    return new Promise(async (resolve, reject) => {
      try {
        const response = await GET_DATA(getURI(endpoint), payload, true);
        resolve(response);
      } catch (error) {
        reject(error);
      }
    });
  };

  useEffect(() => {
    if (clientData) {
      setEmailList(clientData.emails);
      // setEmail(clientData.emails[0]);
    }
  }, []);

  useEffect(() => {
    if (isOpen) {
      getUsers();
    }
  }, [isOpen]);

  const emailOptions = [
    ...emailList.map((x) => ({
      value: x.id_Email,
      label: x.endereco_Email,
    })),
  ];

  useEffect(() => {
    if (selectedEmail === null) {
      setCheckbuttonToSend(false);
      return false;
    }
    setCheckbuttonToSend(true);
  }, [email, selectedEmail]);

  const [send, setSend] = useState(false);
  const [copied, setCopied] = useState(false);
  const handleCopyClipboard = () => {
    navigator.clipboard.writeText(pixPayload);
    setCopied(true);
    setTimeout(() => {
      setCopied(false);
    }, 1000);
  };

  const handleSendEmail = async () => {
    setSend(true);
    if (qrCodeRef.current) {
      const svgElement = qrCodeRef.current.querySelector("svg");
      const svgData = btoa(new XMLSerializer().serializeToString(svgElement));
      const emailHtml = `
        <html>
          <body style="text-align: center;">
            <h2>Seu QR Code PIX - Parcelas ${nrParcelas.join(", ")}</h2>
            <p>Segue abaixo o QR Code para pagamento via PIX:</p>
            <div style="margin: 20px 0;">
              <img src="data:image/svg+xml;base64,${svgData}" />
            </div>
            <p>Código PIX para copiar e colar:</p>
            <p>${pixPayload}</p>
            <p>Valor: R$ ${formatThousands(dados.vlNegociado)}</p>
            <p>Vencimento: ${formatDate(dados.dtNegociacao)}</p>
          </body>
        </html>
      `;

      await saveEmail({
        contractId: financiadoData.id_Contrato,
        groupId: financiadoData.id_Grupo,
        crmId: financiadoData.coddatacob === "GVC" ? 2 : 1,
        userId: user.id,
        contract: financiadoData.numero_Contrato,
        subject: `PIX - Parcelas ${nrParcelas.join(", ")}`,
        destination: email,
        fromCopy: "",
        content: emailHtml,
        attachments: [],
        sendDate: new Date(),
        status: 0,
        isBodyHtml: true,
        active: true,
      });
    }
    setSend(false);
  };

  const saveEmail = async (email) => {
    const response = await postApi(email, "sendFromEmail");
    if (response?.success) {
      toast.success("Requisição de envio de email criada com sucesso.");
    } else {
      toast.error("Erro ao criar requisição de envio de email.");
    }
  };

  return (
    <>
      <CModal size="lg" show={isOpen} onClose={onClose} closeOnBackdrop={false}>
        <CModalHeader closeButton>Emissão de Pix</CModalHeader>
        <CModalBody>
          {isLoading ? (
            <CardLoading Title={"Gerando Pix..."} />
          ) : (
            <CRow>
              <CCol>
                <CCard>
                  <CCardHeader className="py-1">Dados de Envio</CCardHeader>
                  <CCardBody className="py-1">
                    <CForm>
                      <CFormGroup>
                        <CLabel>Emails disponíveis</CLabel>
                        <Select
                          value={selectedEmail}
                          options={emailOptions}
                          onChange={handleEmailChange}
                          placeholder="Selecione"
                        />
                        <CLabel>Email destinatário</CLabel>
                        <CInput
                          type="text"
                          value={email}
                          onChange={handleInputChange}
                        />
                        {isLoadingUsers ? (
                          <CardLoading />
                        ) : (
                          <CForm>
                            <CFormGroup>
                              <CLabel className="mt-2">
                                Nome do Negociador
                              </CLabel>
                              <Select
                                value={selectedNegociador}
                                options={negociadorOptions}
                                onChange={handleNegociadorChange}
                                placeholder="Selecione"
                              />
                            </CFormGroup>
                          </CForm>
                        )}
                      </CFormGroup>
                    </CForm>
                  </CCardBody>
                </CCard>
                <CCard>
                  <CCardHeader>Valores</CCardHeader>
                  <CCardBody>
                    <CRow>
                      <CCol>
                        <CLabel>Total do Pix</CLabel> <br />
                        <CLabel>R$ {formatThousands(dados.vlNegociado)}</CLabel>
                      </CCol>
                      <CCol>
                        <CLabel>Vencimento</CLabel> <br />
                        <CLabel>{formatDate(dados.dtNegociacao)}</CLabel>
                      </CCol>
                    </CRow>
                  </CCardBody>
                </CCard>
              </CCol>
              <CCol>
                <CCard>
                  <CCardHeader className="py-1">QR Code PIX</CCardHeader>
                  <CCardBody className="py-1">
                    {pixPayload && (
                      <>
                        <div
                          style={{
                            margin: "13px 0",
                            padding: "10px",
                            backgroundColor: "white",
                            border: "1px solid #eee",
                          }}
                          ref={qrCodeRef}
                        >
                          <QRCodeSVG value={pixPayload} size={300} level="M" />
                        </div>
                        <div className="text-center">
                          <div className="mb-2">
                            <CButton
                              color="primary"
                              onClick={handleCopyClipboard}
                            >
                              {copied ? "Copiado!" : "Copiar"}
                            </CButton>
                          </div>
                          <div className="mb-2">
                            <CButton color="warning" onClick={handleSendEmail}>
                              {send ? "Enviando!" : "Enviar por Email"}
                            </CButton>
                          </div>
                        </div>
                      </>
                    )}
                    {!pixPayload && (
                      <div className="text-center my-2">
                        <CButton
                          type="submit"
                          color="primary"
                          onClick={handleSubmit}
                          disabled={!checkbuttonToSend || isLoading}
                        >
                          {isLoading ? <LoadingComponent /> : "Gerar QR Code"}
                        </CButton>
                      </div>
                    )}
                  </CCardBody>
                </CCard>
              </CCol>
            </CRow>
          )}
          <CRow>
            <CCol className="d-flex justify-content-end">
              <CButton
                color="primary"
                className="mr-2"
                onClick={() => setContatoModal(true)}
                disabled={isLoading}
              >
                Dados de Contato
              </CButton>
              <CButton
                color="secondary"
                className="mr-2"
                onClick={handleClose}
                disabled={isLoading}
              >
                Cancelar
              </CButton>
            </CCol>
          </CRow>
        </CModalBody>
      </CModal>
      {contatoModal && (
        <DadosContatoModal
          isOpen={contatoModal}
          onClose={() => setContatoModal(false)}
          clientData={clientData}
        />
      )}
    </>
  );
};

export default PixModal;
