export interface CartaDiluicaoInfosType {
  id: string;
  tipoCartaDiluicaoId: string;
  pedidoId: string;
  clientePrincipal: string;
  adversoPrincipal: string;
  grupoCotaContrato: string;
  dataRetornoPagamento: string;
  mesesParcelas: string;
  dataBase: string; // ISO date string (YYYY-MM-DD)
  createdAt: string; // ISO datetime string
}

export interface CartaDiluicaoApiResponse {
  success: boolean;
  data: CartaDiluicaoInfosType;
  message?: string;
}
